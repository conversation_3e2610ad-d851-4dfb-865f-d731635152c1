package user

import (
	"context"
	"strings"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model"
	"github.com/dsoplabs/dinbora-backend/internal/model/auth"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Hard coding my area this is the number of features currently the user has access to
const AVAILABLE_FEATURES = 4

// CRUD
func (s *service) Create(ctx context.Context, user *model.User, referralCode string) error {
	foundUser, err := s.Repository.FindByEmail(ctx, user.Email)
	if err != nil {
		// If it's a "not found" error, continue with user creation
		if domainErr, ok := err.(*errors.DomainError); !ok || domainErr.Kind() != errors.NotFound {
			// Stop creation for any other type of error (internal errors, etc.), propagate it.
			return err
		}
	} else if foundUser != nil {
		return errors.New(errors.Service, errors.UserConflictExists, errors.Conflict, nil)
	}

	// Handle referral code
	var referee *model.User
	if referralCode != "" {
		referee, err = s.Repository.FindByReferral(ctx, referralCode)
		if err != nil {
			return err
		}

		// Update user with referral code and referring user ID
		user.UsedReferralCode = referralCode
		user.ReferringUserID = referee.ID
	}

	// Start user onboarding
	if err := s.onboarding(user); err != nil {
		return err
	}

	userID, err := s.Repository.Create(ctx, user)
	if err != nil {
		return err
	}

	userObjectID, err := primitive.ObjectIDFromHex(userID)
	if err != nil {
		return err
	}

	user.ID = userID
	user.ObjectID = userObjectID

	// Propagate error. It should be treaten in the Initialization of each service.
	if err := s.ProgressionService.Initialize(ctx, userID); err != nil {
		s.Repository.Delete(ctx, user.ObjectID)
		return err
	}
	if err := s.VaultService.Initialize(ctx, userID, referee != nil); err != nil {
		s.Repository.Delete(ctx, user.ObjectID)
		return err
	}
	if err := s.ContractService.Initialize(ctx, userID); err != nil {
		s.Repository.Delete(ctx, user.ObjectID)
		return err
	}
	if err := s.DreamboardService.Initialize(ctx, userID); err != nil {
		s.Repository.Delete(ctx, user.ObjectID)
		return err
	}

	if err := s.FinancialDNAService.Initialize(ctx, userID); err != nil {
		s.Repository.Delete(ctx, user.ObjectID)
		return err
	}

	if err := s.FinancialSheetService.Initialize(ctx, userID, user.Name); err != nil {
		s.Repository.Delete(ctx, user.ObjectID)
	}

	// Update referee vault if referral code is used.
	if referralCode != "" && referee != nil {
		refereeVault, err := s.VaultService.FindByUser(ctx, referee.ID)
		if err != nil {
			return err
		}
		refereeVault.Coins = refereeVault.Coins + 10

		err = s.VaultService.Update(ctx, refereeVault)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *service) Find(ctx context.Context, id string) (*model.User, error) {
	return s.findUser(ctx, func(ctx context.Context) (*model.User, error) {
		userObjectID, err := primitive.ObjectIDFromHex(id)
		if err != nil {
			return nil, errors.New(errors.Service, errors.UserInvalidID, errors.Validation, err)
		}
		return s.Repository.Find(ctx, userObjectID)
	})
}

func (s *service) FindAll(ctx context.Context) ([]*model.User, error) {
	users, err := s.Repository.FindAll(ctx)
	if err != nil {
		return nil, err
	}
	return users, nil
}

func (s *service) FindByEmail(ctx context.Context, email string) (*model.User, error) {
	user, err := s.Repository.FindByEmail(ctx, email)
	if err != nil {
		return nil, err
	}
	return user, nil
}

func (s *service) FindByReferral(ctx context.Context, referralCode string) (*model.User, error) {
	user, err := s.Repository.FindByReferral(ctx, referralCode)
	if err != nil {
		return nil, err
	}
	return user, nil
}

func (s *service) FindDeletedByEmail(ctx context.Context, email string) (*model.User, error) {
	user, err := s.Repository.FindDeletedByEmail(ctx, email)
	if err != nil {
		return nil, err
	}
	return user, nil
}

// External Integration - Stripe
func (s *service) FindByExternalCode(ctx context.Context, externalCode string) (*model.User, error) {
	user, err := s.Repository.FindByExternalCode(ctx, externalCode)
	if err != nil {
		return nil, err
	}
	return user, nil
}

func (s *service) FindDeletedByExternalCode(ctx context.Context, externalCode string) (*model.User, error) {
	user, err := s.Repository.FindDeletedByExternalCode(ctx, externalCode)
	if err != nil {
		return nil, err
	}
	return user, nil
}

// External Integration - Kiwify
func (s *service) FindByExternalCodeKiwify(ctx context.Context, externalCodeKiwify string) (*model.User, error) {
	user, err := s.Repository.FindByExternalCodeKiwify(ctx, externalCodeKiwify)
	if err != nil {
		return nil, err
	}
	return user, nil
}

func (s *service) FindDeletedByExternalCodeKiwify(ctx context.Context, externalCodeKiwify string) (*model.User, error) {
	user, err := s.Repository.FindDeletedByExternalCodeKiwify(ctx, externalCodeKiwify)
	if err != nil {
		return nil, err
	}
	return user, nil
}

// Update performs a full update with validation
func (s *service) Update(ctx context.Context, user *model.User) error {
	if err := user.Validate(); err != nil {
		return err
	}

	if user.ObjectID.IsZero() {
		objID, err := primitive.ObjectIDFromHex(user.ID)
		if err != nil {
			return errors.New(errors.Service, "invalid user ID", errors.Validation, err)
		}
		user.ObjectID = objID
	}

	// Since user role manipulation is not allowed, we need to fetch the user from the database to get the current roles
	foundUser, err := s.Repository.Find(ctx, user.ObjectID)
	if err != nil {
		return err
	}
	user.Roles = foundUser.Roles

	return s.Repository.Update(ctx, user)
}

// Patch performs a partial update while preserving protected fields
func (s *service) Patch(ctx context.Context, user *model.User, patchData *model.User) error {
	// Keep existing roles since they cannot be modified via patch
	existingRoles := user.Roles

	// Prepare update with patch data
	if err := user.PrepareUpdate(patchData); err != nil {
		return err
	}

	// Restore roles to prevent modification
	user.Roles = existingRoles

	// Apply update without validation since it's a partial update
	return s.Repository.Update(ctx, user)
}

func (s *service) Sync(ctx context.Context, user *model.User) error {
	return s.syncCustomer(ctx, user)
}

func (s *service) Delete(ctx context.Context, id string, reason *model.DeleteReason) error {
	foundUser, err := s.Find(ctx, id)
	if err != nil {
		return err
	}

	// Try to delete the customer from stipe if it has external code
	if foundUser.ExternalCode != "" {
		if err := s.CustomerService.Delete(foundUser.ExternalCode); err != nil {
			return err
		}
	}

	deletedUser := model.DeletedUser{
		User:         foundUser,
		DeleteReason: reason,
	}

	if err = s.Repository.CreateDelete(ctx, &deletedUser); err != nil {
		return err
	}

	err = s.Repository.Delete(ctx, foundUser.ObjectID)
	if err != nil {
		return err
	}
	return nil
}

// Card CRUD
func (s *service) FindCard(ctx context.Context, userId string) (*model.UserCard, error) {
	// Updated call to receive walletID string instead of wallet slice
	foundUser, foundProgressionAchievements, foundVault, currentSequence, foundWalletID, err := s.getUserCardData(ctx, userId)
	if err != nil {
		return nil, err
	}

	userFirstName := strings.Split(foundUser.Name, " ")[0]

	// Check if foundProgressionAchievements is nil and assign an empty slice if it is
	if foundProgressionAchievements == nil {
		foundProgressionAchievements = []*content.Achievement{}
	}

	userCard := &model.UserCard{
		ID:                foundUser.ID,
		Name:              userFirstName,
		Email:             foundUser.Email,
		PhotoURL:          foundUser.PhotoURL,
		Coins:             foundVault.Coins,
		AchievementsCount: int64(len(foundProgressionAchievements)),
		CurrentSequence:   currentSequence,
		Diamonds:          foundVault.Diamonds,
		AvailableFeatures: AVAILABLE_FEATURES,
		Achievements:      foundProgressionAchievements,
		ReferralCode:      foundUser.ReferralCode,
		// Wallet and Investments are set below based on foundWalletID
	}

	// Set Wallet ID if found, removed Investments count as it's not directly available
	if foundWalletID != "" {
		userCard.Wallet = foundWalletID
		// userCard.Investments = ... // Removed as per plan
	}

	return userCard, nil
}

// Utility
func (s *service) FindUserWallet(ctx context.Context, userId string) (string, error) {
	// Updated call to receive walletID string instead of wallet slice
	foundWalletID, err := s.ContractService.FindFirstWalletIDByCustomer(ctx, userId)
	if err != nil {
		// Propagate error from FindFirstWalletIDByCustomer
		return "", err
	}

	return foundWalletID, nil
}

func (s *service) OnlyAdmin(ctx context.Context, id string) error {
	admins, err := s.Repository.FindAdmins(ctx)
	if err != nil {
		return err
	}

	if admins == nil || len(admins) <= 0 {
		return errors.New(errors.Service, errors.AdminUsersNotFound, errors.NotFound, nil)
	}

	onlyAdmin := true
	for _, admin := range admins {
		if admin.ID != id {
			onlyAdmin = false
		}
	}

	if onlyAdmin {
		return errors.New(errors.Service, errors.AdminUsersNotFound, errors.NotFound, nil)
	}

	return nil
}

// Helper

func (s *service) findUser(ctx context.Context, findFn func(ctx context.Context) (*model.User, error)) (*model.User, error) {
	user, err := findFn(ctx)
	if err != nil {
		return nil, err
	}
	return user, nil
}

func (s *service) syncCustomer(ctx context.Context, user *model.User) error {
	if user.ExternalCode == "" {
		createdCustomer, err := s.CustomerService.Create(user)
		if err != nil {
			return err
		}

		user.ExternalCode = createdCustomer.ID
		err = s.Patch(ctx, user, &model.User{ExternalCode: createdCustomer.ID})
		if err != nil {
			return err
		}
		return nil
	}
	_, err := s.CustomerService.Update(user)
	if err != nil {
		return err
	}

	return nil
}

// Helper
// Updated return signature to return wallet ID string instead of wallet slice
func (s *service) getUserCardData(ctx context.Context, id string) (*model.User, []*content.Achievement, *model.Vault, int, string, error) {
	foundUser, err := s.Find(ctx, id)
	if err != nil {
		return nil, nil, nil, 0, "", err
	}

	foundProgressionAchievementsContent, err := s.ProgressionService.FindAllAchievementsContent(ctx, foundUser.ID)
	if err != nil {
		// Handle potential "not found" for achievements gracefully if needed, otherwise propagate
		return nil, nil, nil, 0, "", err
	}
	// Filter only conquered achievements
	foundProgressionAchievementsContent = filterConqueredAchievements(foundProgressionAchievementsContent)

	foundVault, err := s.VaultService.FindByUser(ctx, foundUser.ID)
	if err != nil {
		// Handle potential "not found" for vault gracefully if needed, otherwise propagate
		return nil, nil, nil, 0, "", err
	}

	foundFinancialSheet, err := s.FinancialSheetService.FindByUser(ctx, foundUser.ID)
	if err != nil {
		// Handle potential "not found" for financial sheet gracefully if needed, otherwise propagate
		return nil, nil, nil, 0, "", err
	}

	// Replaced WalletService.FindByUser with ContractService.FindFirstWalletIDByCustomer
	foundWalletID, err := s.ContractService.FindFirstWalletIDByCustomer(ctx, foundUser.ID)
	if err != nil {
		// Propagate error from FindFirstWalletIDByCustomer
		return nil, nil, nil, 0, "", err
	}

	// Return wallet ID string
	return foundUser, foundProgressionAchievementsContent, foundVault, foundFinancialSheet.Points.Current, foundWalletID, nil
}

// filterConqueredAchievements filters only conquered achievements
func filterConqueredAchievements(achievements []*content.Achievement) []*content.Achievement {
	var filteredAchievements []*content.Achievement
	for _, achievement := range achievements {
		if achievement.Conquered {
			filteredAchievements = append(filteredAchievements, achievement)
		}
	}
	return filteredAchievements
}

func (s *service) onboarding(user *model.User) error {
	if err := user.Onboarding.AgeRange.IsValid(); err != nil {
		return err
	}
	user.Onboarding.AgeRange.AddLabel()

	if err := user.Onboarding.FinancialSituation.IsValid(); err != nil {
		return err
	}
	user.Onboarding.FinancialSituation.AddLabel()

	if err := user.Onboarding.FinancialGoal.IsValid(); err != nil {
		return err
	}
	user.Onboarding.FinancialGoal.AddLabel()

	for i, personalInterest := range user.Onboarding.PersonalInterests {
		if err := personalInterest.IsValid(); err != nil {
			return err
		}
		user.Onboarding.PersonalInterests[i].AddLabel()
	}

	user.Onboarding.CompletedAt = time.Now()

	return nil
}

// Role-based filtering methods

// FindAllWithRoleFilter returns all users that the requesting user can access based on their roles
func (s *service) FindAllWithRoleFilter(ctx context.Context, requestingUserID string, requestingUserRoles []string) ([]*model.User, error) {
	// Check permissions
	permissions := auth.GetPermissions(requestingUserRoles)

	// Admin can access all users
	if permissions.CanAccessAllData {
		return s.Repository.FindAll(ctx)
	}

	// Build filter based on user roles and permissions
	// filter := s.RBACService.BuildUserFilter(ctx, requestingUserID, requestingUserRoles)

	// Use repository method with filter (this would need to be implemented in the repository)
	// For now, we'll use the existing FindAll and filter in memory (not optimal for production)
	allUsers, err := s.Repository.FindAll(ctx)
	if err != nil {
		return nil, err
	}

	var filteredUsers []*model.User
	for _, user := range allUsers {
		if s.RBACService.CanAccessResource(ctx, requestingUserID, requestingUserRoles, user.ID) {
			filteredUsers = append(filteredUsers, user)
		}
	}

	return filteredUsers, nil
}

// FindWithRoleFilter returns a specific user if the requesting user can access them
func (s *service) FindWithRoleFilter(ctx context.Context, targetUserID string, requestingUserID string, requestingUserRoles []string) (*model.User, error) {
	// Check if the requesting user can access the target user
	canAccess, err := s.CanAccessUser(ctx, requestingUserID, requestingUserRoles, targetUserID)
	if err != nil {
		return nil, err
	}

	if !canAccess {
		return nil, errors.New(errors.Service, "access denied", errors.Forbidden, nil)
	}

	// If access is allowed, return the user
	return s.Find(ctx, targetUserID)
}

// CanAccessUser checks if the requesting user can access the target user based on roles and permissions
func (s *service) CanAccessUser(ctx context.Context, requestingUserID string, requestingUserRoles []string, targetUserID string) (bool, error) {
	// Users can always access their own data
	if requestingUserID == targetUserID {
		return true, nil
	}

	// Use RBAC service to check access
	return s.RBACService.CanAccessResource(ctx, requestingUserID, requestingUserRoles, targetUserID), nil
}
