package gamification

import (
	"context"
	"net/http"

	"github.com/dsoplabs/dinbora-backend/internal/api/middlewares"
	"github.com/dsoplabs/dinbora-backend/internal/api/token"
	"github.com/dsoplabs/dinbora-backend/internal/service/gamification"
	"github.com/labstack/echo/v4"
)

type Controller interface {
	// Routes
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)

	// Endpoints
	GetUserAchievements() echo.HandlerFunc
}

type controller struct {
	Service gamification.Service
}

func New(service gamification.Service) Controller {
	return &controller{
		Service: service,
	}
}

// Routes
func (c *controller) RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group) {
	gamificationGroup := currentGroup.Group("/gamification", middlewares.AuthGuard())

	// User achievements endpoint
	gamificationGroup.GET("/achievements", c.GetUserAchievements())
}

// GetUserAchievements returns all achievements for the authenticated user
func (c *controller) GetUserAchievements() echo.HandlerFunc {
	return func(ctx echo.Context) error {
		// Extract user ID from JWT token
		userToken, err := token.GetClaimsFromRequest(ctx.Request())
		if err != nil {
			return err
		}

		// Get user achievements
		achievements, err := c.Service.GetUserAchievements(ctx.Request().Context(), userToken.Uid)
		if err != nil {
			return err
		}

		return ctx.JSON(http.StatusOK, map[string]interface{}{
			"achievements": achievements,
		})
	}
}
