package dashboard

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// FindFinancialMap implements the unified approach for dashboard data
func (s *service) FindFinancialMap(ctx context.Context, userID string) (*dashboard.FinancialMap, error) {
	// Try to fetch from unified collection
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil && !isNotFoundError(err) {
		return nil, err
	}

	// If found, update with live income data and persist if changed
	if financialMap != nil {
		// Create a copy of the original income sources and emergency fund for comparison
		originalIncomeSources := make([]*dashboard.IncomeSource, len(financialMap.IncomeSources))
		copy(originalIncomeSources, financialMap.IncomeSources)
		originalFinancialMap := &dashboard.FinancialMap{
			IncomeSources: originalIncomeSources,
			StrategicFund: financialMap.StrategicFund,
		}
		// Check if any emergency fund was created after a manual update
		currentStrategicFund, err := s.aggregateStrategicFundFromTransactions(ctx, userID)
		if err != nil {
			return nil, err
		}
		financialMap.StrategicFund = currentStrategicFund

		updatedFinancialMap, err := s.updateFinancialMapWithLiveData(ctx, financialMap)
		if err != nil {
			return nil, err
		}

		// Check if income sources have changed and persist if necessary
		if s.hasIncomeSourcesChanged(originalFinancialMap, updatedFinancialMap) {
			if err := updatedFinancialMap.PrepareUpdate(); err != nil {
				return nil, err
			}
			// Don't need to update the net worth history since cron job will store monthly snapshots and this month will be retrieved dynamicly
			tmpNetWorthHistory := updatedFinancialMap.NetWorthHistory
			updatedFinancialMap.NetWorthHistory = originalFinancialMap.NetWorthHistory
			if err := s.Repository.UpdateFinancialMap(ctx, updatedFinancialMap); err != nil {
				return nil, err
			}
			updatedFinancialMap.NetWorthHistory = tmpNetWorthHistory
		}

		// Persist emergency fund if necessary
		if originalFinancialMap.StrategicFund.CurrentValue != updatedFinancialMap.StrategicFund.CurrentValue {
			if err := updatedFinancialMap.PrepareUpdate(); err != nil {
				return nil, err
			}
			if err := updatedFinancialMap.StrategicFund.PrepareUpdate(); err != nil {
				return nil, err
			}
			// Don't need to update the net worth history since cron job will store monthly snapshots and this month will be retrieved dynamicly
			tmpNetWorthHistory := updatedFinancialMap.NetWorthHistory
			updatedFinancialMap.NetWorthHistory = originalFinancialMap.NetWorthHistory
			if err := s.Repository.UpdateFinancialMap(ctx, updatedFinancialMap); err != nil {
				return nil, err
			}
			updatedFinancialMap.NetWorthHistory = tmpNetWorthHistory
		}

		return updatedFinancialMap, nil
	}

	// If not found, create a new financial map with live data (already persisted in createNewFinancialMap)
	return s.createNewFinancialMap(ctx, userID)
}

// updateFinancialMapWithLiveData updates the financial map with live income data from financial sheet
func (s *service) updateFinancialMapWithLiveData(ctx context.Context, financialMap *dashboard.FinancialMap) (*dashboard.FinancialMap, error) {
	// Fetch live income sources from financial sheet transactions
	liveIncomeSources, err := s.aggregateIncomeSourcesFromTransactions(ctx, financialMap.UserID)
	if err != nil {
		return nil, err
	}

	// Preserve user-created (non-blocked) income sources and merge with live data
	mergedIncomeSources := make([]*dashboard.IncomeSource, 0)
	var monthlyIncome monetary.Amount

	// First, add all user-created (non-blocked) income sources
	for _, existingSource := range financialMap.IncomeSources {
		if !existingSource.Blocked {
			mergedIncomeSources = append(mergedIncomeSources, existingSource)
			monthlyIncome += existingSource.MonthlyAmount
		}
	}

	// Then, add all live income sources from financial sheet (blocked)
	for _, liveSource := range liveIncomeSources {
		mergedIncomeSources = append(mergedIncomeSources, liveSource)
		monthlyIncome += liveSource.MonthlyAmount
	}

	// Update financial map with merged income data
	financialMap.IncomeSources = mergedIncomeSources
	financialMap.MonthlyIncome = monthlyIncome

	// Create dynamic snapshot for current month
	var strategicFundValue monetary.Amount
	if financialMap.StrategicFund != nil {
		strategicFundValue = financialMap.StrategicFund.CurrentValue
	}

	currentSnapshot := &dashboard.NetWorthSnapshot{
		UserID:             financialMap.UserID,
		Date:               time.Now(),
		StrategicFundValue: strategicFundValue,
		InvestmentsValue:   financialMap.TotalInvestments,
		AssetsValue:        financialMap.TotalAssets,
		TotalValue:         strategicFundValue + financialMap.TotalInvestments + financialMap.TotalAssets,
	}

	// Add current snapshot to history (limit to 12 months total)
	financialMap.NetWorthHistory = append(financialMap.NetWorthHistory, currentSnapshot)
	if len(financialMap.NetWorthHistory) > 12 {
		financialMap.NetWorthHistory = financialMap.NetWorthHistory[len(financialMap.NetWorthHistory)-12:]
	}

	return financialMap, nil
}

// createNewFinancialMap creates a new financial map with live data
func (s *service) createNewFinancialMap(ctx context.Context, userID string) (*dashboard.FinancialMap, error) {
	// Fetch live income sources from financial sheet transactions
	incomeSources, err := s.aggregateIncomeSourcesFromTransactions(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Calculate monthly income
	var monthlyIncome monetary.Amount
	incomeSourcesSlice := make([]*dashboard.IncomeSource, 0, len(incomeSources))
	for _, source := range incomeSources {
		incomeSourcesSlice = append(incomeSourcesSlice, source)
		monthlyIncome += source.MonthlyAmount
	}

	// Fetch live emergency fund from financial sheet if exists
	strategicFund, err := s.aggregateStrategicFundFromTransactions(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Create new financial map
	financialMap := &dashboard.FinancialMap{
		UserID:           userID,
		MonthlyIncome:    monthlyIncome,
		StrategicFund:    strategicFund, // Will be 0 if no emergency fund transactions exist
		TotalInvestments: 0,
		TotalAssets:      0,
		NetWorthHistory:  []*dashboard.NetWorthSnapshot{},
		IncomeSources:    incomeSourcesSlice,
		Investments:      []*dashboard.Investment{},
		Assets:           []*dashboard.Asset{},
	}

	// Prepare for creation
	if err := financialMap.PrepareCreate(); err != nil {
		return nil, err
	}

	// Save to database
	if err := s.Repository.SaveFinancialMap(ctx, financialMap); err != nil {
		return nil, err
	}

	// Create a dynamic snapshot for the current month
	dynamicNetWorthSnapshot := &dashboard.NetWorthSnapshot{
		UserID:             userID,
		Date:               time.Now(),
		StrategicFundValue: strategicFund.CurrentValue,
		InvestmentsValue:   0,
		AssetsValue:        0,
		TotalValue:         strategicFund.CurrentValue,
	}

	financialMap.NetWorthHistory = append(financialMap.NetWorthHistory, dynamicNetWorthSnapshot)

	return financialMap, nil
}

// aggregateIncomeSourcesFromTransactions fetches income data from financial sheet transactions
func (s *service) aggregateIncomeSourcesFromTransactions(ctx context.Context, userID string) (map[string]*dashboard.IncomeSource, error) {
	// Get current month and year
	now := time.Now()
	currentYear := now.Year()
	currentMonth := int(now.Month())

	// Fetch income transactions from financial sheet
	transactions, err := s.FinancialSheetService.FindAllTransactions(ctx, userID, financialsheet.CategoryTypeIncome, currentYear, currentMonth, false)
	if err != nil {
		return nil, err
	}

	// Aggregate income by money source
	incomeMap := make(map[string]*dashboard.IncomeSource)
	for _, transaction := range transactions {
		// Create a Category instance to get the MoneySourceInfo
		cat := financialsheet.Category{
			Identifier: transaction.Category,
			Type:       transaction.Type,
		}
		moneySourceInfo := cat.GetMoneySourceInfo(transaction.MoneySource)

		// Use the money source name as the key for grouping
		key := moneySourceInfo.Name

		if incomeSource, exists := incomeMap[key]; exists {
			incomeSource.MonthlyAmount += transaction.Value
		} else {
			incomeMap[key] = &dashboard.IncomeSource{
				UserID:        userID,
				Name:          moneySourceInfo.Name,
				MonthlyAmount: transaction.Value,
				MoneySource:   transaction.MoneySource,
				Icon:          moneySourceInfo.Icon,
				Blocked:       true, // Income sources from financial sheet are blocked (read-only)
			}
			// Prepare for creation to set timestamps
			incomeMap[key].PrepareCreate()
		}
	}

	return incomeMap, nil
}

// aggregateStrategicFundFromTransactions fetches emergency fund data from financial sheet transactions
func (s *service) aggregateStrategicFundFromTransactions(ctx context.Context, userID string) (*dashboard.StrategicFund, error) {
	// If StrategicFund exists, update with recent value after the manual update and return
	existingStrategicFund, err := s.Repository.FindStrategicFund(ctx, userID)
	if err == nil {
		// Get current month and year
		now := time.Now()
		currentYear := now.Year()
		currentMonth := int(now.Month())

		// Check if there are any transactions after the last update date
		transactions, err := s.FinancialSheetService.FindAllTransactions(ctx, userID, financialsheet.CategoryTypeCostsOfLiving, currentYear, currentMonth, false)
		if err != nil {
			return nil, err
		}

		lastTransactions := make([]*financialsheet.Transaction, 0)
		for _, transaction := range transactions {
			if transaction.Date.After(*existingStrategicFund.UpdatedAt) {
				lastTransactions = append(lastTransactions, transaction)
			}
		}

		// If there are no transactions after the last update, return the existing strategic fund
		if len(lastTransactions) == 0 {
			return existingStrategicFund, nil
		}

		// Aggregate emergency fund amount from last transactions
		strategicFundValue := existingStrategicFund.CurrentValue
		for _, transaction := range lastTransactions {
			if transaction.Category == financialsheet.CategoryIdentifierPersonalReserves && transaction.MoneySource == financialsheet.MoneySourceOpt3 {
				strategicFundValue += transaction.Value
			}
		}

		// Update existing strategic fund
		existingStrategicFund.CurrentValue = strategicFundValue

		return existingStrategicFund, nil
	} else if !isNotFoundError(err) {
		return nil, err
	}

	// Fetch all emergency fund transactions from financial sheet
	transactions, err := s.FinancialSheetService.FindAllTransactions(ctx, userID, financialsheet.CategoryTypeCostsOfLiving, 0, 0, false)
	if err != nil {
		return nil, err
	}

	// Aggregate emergency fund amount
	var strategicFundValue monetary.Amount
	for _, transaction := range transactions {
		if transaction.Category == financialsheet.CategoryIdentifierPersonalReserves && transaction.MoneySource == financialsheet.MoneySourceOpt3 {
			strategicFundValue += transaction.Value
		}
	}

	// Create emergency fund object
	strategicFund := &dashboard.StrategicFund{
		ObjectID:     primitive.NewObjectID(), // Generate unique ObjectID for embedded document
		UserID:       userID,
		CurrentValue: strategicFundValue,
		GoalValue:    4200000, // Default goal value (R$ 42,000.00)
	}

	// Prepare for creation to set timestamps
	strategicFund.PrepareCreate()

	return strategicFund, nil
}

// hasIncomeSourcesChanged compares income sources between original and updated financial maps
func (s *service) hasIncomeSourcesChanged(original, updated *dashboard.FinancialMap) bool {
	// Quick check: different number of income sources
	if len(original.IncomeSources) != len(updated.IncomeSources) {
		return true
	}

	// If both have no income sources, no change
	if len(original.IncomeSources) == 0 && len(updated.IncomeSources) == 0 {
		return false
	}

	// Create maps for easier comparison
	originalMap := make(map[string]*dashboard.IncomeSource)
	for _, source := range original.IncomeSources {
		originalMap[source.Name] = source
	}

	updatedMap := make(map[string]*dashboard.IncomeSource)
	for _, source := range updated.IncomeSources {
		updatedMap[source.Name] = source
	}

	// Check if any income source has changed
	for name, updatedSource := range updatedMap {
		originalSource, exists := originalMap[name]
		if !exists {
			return true // New income source
		}

		// Compare key fields that matter for income sources
		if originalSource.MonthlyAmount != updatedSource.MonthlyAmount ||
			originalSource.MoneySource != updatedSource.MoneySource ||
			originalSource.Icon != updatedSource.Icon ||
			originalSource.Blocked != updatedSource.Blocked {
			return true
		}
	}

	// Check if any original income source was removed
	for name := range originalMap {
		if _, exists := updatedMap[name]; !exists {
			return true // Income source was removed
		}
	}

	return false
}

// Specialized CRUD operations for individual components within the unified collection

// CreateIncomeSource creates a new income source within the unified financial map
func (s *service) CreateIncomeSource(ctx context.Context, userID string, name string, monthlyAmount monetary.Amount) (*dashboard.IncomeSource, error) {
	// Get or create financial map
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil && !isNotFoundError(err) {
		return nil, err
	}

	if financialMap == nil {
		// Create new financial map if it doesn't exist
		financialMap, err = s.createNewFinancialMap(ctx, userID)
		if err != nil {
			return nil, err
		}
	}

	// Create new income source
	incomeSource := &dashboard.IncomeSource{
		ObjectID:      primitive.NewObjectID(), // Generate unique ObjectID for embedded document
		UserID:        userID,
		Name:          name,
		MonthlyAmount: monthlyAmount,
		Blocked:       false, // User-created income sources are not blocked (editable)
	}

	if err := incomeSource.PrepareCreate(); err != nil {
		return nil, err
	}

	// Add to financial map
	financialMap.IncomeSources = append(financialMap.IncomeSources, incomeSource)

	// Recalculate monthly income
	var totalMonthlyIncome monetary.Amount
	for _, source := range financialMap.IncomeSources {
		totalMonthlyIncome += source.MonthlyAmount
	}
	financialMap.MonthlyIncome = totalMonthlyIncome

	// Update financial map
	if err := financialMap.PrepareUpdate(); err != nil {
		return nil, err
	}

	if err := s.Repository.UpdateFinancialMap(ctx, financialMap); err != nil {
		return nil, err
	}

	return incomeSource, nil
}

// FindIncomeSources returns income sources from the unified financial map with live data
func (s *service) FindIncomeSources(ctx context.Context, userID string) ([]*dashboard.IncomeSource, error) {
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil {
		if isNotFoundError(err) {
			// If no financial map exists, create one with live data (already persisted in createNewFinancialMap)
			financialMap, err = s.createNewFinancialMap(ctx, userID)
			if err != nil {
				return nil, err
			}
		} else {
			return nil, err
		}
	} else {
		// Create a copy of the original income sources for comparison
		originalIncomeSources := make([]*dashboard.IncomeSource, len(financialMap.IncomeSources))
		copy(originalIncomeSources, financialMap.IncomeSources)
		originalFinancialMap := &dashboard.FinancialMap{
			IncomeSources: originalIncomeSources,
		}

		// Update financial map with live income data and persist if changed
		updatedFinancialMap, err := s.updateFinancialMapWithLiveData(ctx, financialMap)
		if err != nil {
			return nil, err
		}

		// Check if income sources have changed and persist if necessary
		if s.hasIncomeSourcesChanged(originalFinancialMap, updatedFinancialMap) {
			if err := updatedFinancialMap.PrepareUpdate(); err != nil {
				return nil, err
			}
			if err := s.Repository.UpdateFinancialMap(ctx, updatedFinancialMap); err != nil {
				return nil, err
			}
		}

		financialMap = updatedFinancialMap
	}

	// Convert slice to pointer slice and set ID fields
	result := make([]*dashboard.IncomeSource, len(financialMap.IncomeSources))
	for i := range financialMap.IncomeSources {
		financialMap.IncomeSources[i].ID = financialMap.IncomeSources[i].ObjectID.Hex()
		result[i] = financialMap.IncomeSources[i]
	}

	return result, nil
}

// UpdateIncomeSource updates an income source within the unified financial map
func (s *service) UpdateIncomeSource(ctx context.Context, id string, name string, monthlyAmount monetary.Amount) error {
	incomeSourceID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Service, "invalid income source ID", errors.Validation, err)
	}

	// Find the financial map that contains this income source
	// Note: This is inefficient as it requires searching across all financial maps
	// The proper solution would be to update the controller to extract userID and pass it here
	financialMap, err := s.findFinancialMapByIncomeSourceID(ctx, incomeSourceID)
	if err != nil {
		return err
	}

	// Find and update the specific income source
	found := false
	for i := range financialMap.IncomeSources {
		if financialMap.IncomeSources[i].ObjectID == incomeSourceID {
			// Check if the income source can be modified (not blocked)
			if !isIncomeSourceModifiable(financialMap.IncomeSources[i]) {
				return errors.New(errors.Service, "cannot update blocked income source - this income source originates from financial sheet and is read-only", errors.Forbidden, nil)
			}

			financialMap.IncomeSources[i].Name = name
			financialMap.IncomeSources[i].MonthlyAmount = monthlyAmount

			// Prepare for update
			if err := financialMap.IncomeSources[i].PrepareUpdate(); err != nil {
				return err
			}
			found = true
			break
		}
	}

	if !found {
		return errors.New(errors.Service, "income source not found", errors.NotFound, nil)
	}

	// Recalculate monthly income
	var totalMonthlyIncome monetary.Amount
	for _, source := range financialMap.IncomeSources {
		totalMonthlyIncome += source.MonthlyAmount
	}
	financialMap.MonthlyIncome = totalMonthlyIncome

	// Update financial map
	if err := financialMap.PrepareUpdate(); err != nil {
		return err
	}

	return s.Repository.UpdateFinancialMap(ctx, financialMap)
}

// DeleteIncomeSource deletes an income source from the unified financial map
func (s *service) DeleteIncomeSource(ctx context.Context, id string) error {
	incomeSourceID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Service, "invalid income source ID", errors.Validation, err)
	}

	// Find the financial map that contains this income source
	// Note: This is inefficient as it requires searching across all financial maps
	// The proper solution would be to update the controller to extract userID and pass it here
	financialMap, err := s.findFinancialMapByIncomeSourceID(ctx, incomeSourceID)
	if err != nil {
		return err
	}

	// Find and remove the specific income source
	found := false
	for i := range financialMap.IncomeSources {
		if financialMap.IncomeSources[i].ObjectID == incomeSourceID {
			// Check if the income source can be modified (not blocked)
			if !isIncomeSourceModifiable(financialMap.IncomeSources[i]) {
				return errors.New(errors.Service, "cannot delete blocked income source - this income source originates from financial sheet and is read-only", errors.Forbidden, nil)
			}

			// Remove the income source from the slice
			financialMap.IncomeSources = append(financialMap.IncomeSources[:i], financialMap.IncomeSources[i+1:]...)
			found = true
			break
		}
	}

	if !found {
		return errors.New(errors.Service, "income source not found", errors.NotFound, nil)
	}

	// Recalculate monthly income
	var totalMonthlyIncome monetary.Amount
	for _, source := range financialMap.IncomeSources {
		totalMonthlyIncome += source.MonthlyAmount
	}
	financialMap.MonthlyIncome = totalMonthlyIncome

	// Update financial map
	if err := financialMap.PrepareUpdate(); err != nil {
		return err
	}

	return s.Repository.UpdateFinancialMap(ctx, financialMap)
}

// findFinancialMapByIncomeSourceID finds the financial map that contains the specified income source
// Note: This is an inefficient workaround. The proper solution would be to pass userID from the controller.
func (s *service) findFinancialMapByIncomeSourceID(ctx context.Context, incomeSourceID primitive.ObjectID) (*dashboard.FinancialMap, error) {
	// Use the repository's FindIncomeSource method which searches within financial maps
	incomeSource, err := s.Repository.FindIncomeSource(ctx, incomeSourceID)
	if err != nil {
		return nil, err
	}

	// Now find the financial map for this user
	return s.Repository.FindFinancialMap(ctx, incomeSource.UserID)
}

// FindStrategicFund returns the emergency fund from the unified financial map
func (s *service) FindStrategicFund(ctx context.Context, userID string) (*dashboard.StrategicFund, error) {
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil {
		return nil, err
	}

	if financialMap.StrategicFund == nil {
		return nil, errors.New(errors.Service, "emergency fund not found", errors.NotFound, nil)
	}

	return financialMap.StrategicFund, nil
}

// UpdateStrategicFund updates the emergency fund within the unified financial map
func (s *service) UpdateStrategicFund(ctx context.Context, userID string, currentValue monetary.Amount) error {
	// Get or create financial map
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil && !isNotFoundError(err) {
		return err
	}

	if financialMap == nil {
		// Create new financial map if it doesn't exist
		financialMap, err = s.createNewFinancialMap(ctx, userID)
		if err != nil {
			return err
		}
	}

	// Update or create emergency fund
	if financialMap.StrategicFund == nil {
		financialMap.StrategicFund = &dashboard.StrategicFund{
			ObjectID:     primitive.NewObjectID(), // Generate unique ObjectID for embedded document
			UserID:       userID,
			CurrentValue: currentValue,
			GoalValue:    4200000, // Default goal value (R$ 42,000.00)
		}
		if err := financialMap.StrategicFund.PrepareCreate(); err != nil {
			return err
		}
	} else {
		financialMap.StrategicFund.CurrentValue = currentValue
		if err := financialMap.StrategicFund.PrepareUpdate(); err != nil {
			return err
		}
	}

	// Update financial map
	if err := financialMap.PrepareUpdate(); err != nil {
		return err
	}

	return s.Repository.UpdateFinancialMap(ctx, financialMap)
}

// UpdateStrategicFundGoal updates the emergency fund goal within the unified financial map
func (s *service) UpdateStrategicFundGoal(ctx context.Context, userID string, goalValue monetary.Amount) error {
	// Get or create financial map
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil && !isNotFoundError(err) {
		return err
	}

	if financialMap == nil {
		// Create new financial map if it doesn't exist
		financialMap, err = s.createNewFinancialMap(ctx, userID)
		if err != nil {
			return err
		}
	}

	// Update or create emergency fund
	if financialMap.StrategicFund == nil {
		financialMap.StrategicFund = &dashboard.StrategicFund{
			ObjectID:     primitive.NewObjectID(), // Generate unique ObjectID for embedded document
			UserID:       userID,
			CurrentValue: 0, // Default current value
			GoalValue:    goalValue,
		}
		if err := financialMap.StrategicFund.PrepareCreate(); err != nil {
			return err
		}
	} else {
		financialMap.StrategicFund.GoalValue = goalValue
		if err := financialMap.StrategicFund.PrepareUpdate(); err != nil {
			return err
		}
	}

	// Update financial map
	if err := financialMap.PrepareUpdate(); err != nil {
		return err
	}

	return s.Repository.UpdateFinancialMap(ctx, financialMap)
}

// Investment operations
func (s *service) CreateInvestment(ctx context.Context, userID string, name string, currentValue monetary.Amount) (*dashboard.Investment, error) {
	// Get or create financial map
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil && !isNotFoundError(err) {
		return nil, err
	}

	if financialMap == nil {
		// Create new financial map if it doesn't exist
		financialMap, err = s.createNewFinancialMap(ctx, userID)
		if err != nil {
			return nil, err
		}
	}

	// Create new investment
	investment := &dashboard.Investment{
		ObjectID:     primitive.NewObjectID(), // Generate unique ObjectID for embedded document
		UserID:       userID,
		Name:         name,
		CurrentValue: currentValue,
	}

	if err := investment.PrepareCreate(); err != nil {
		return nil, err
	}

	// Add to financial map
	financialMap.Investments = append(financialMap.Investments, investment)

	// Recalculate total investments
	var totalInvestments monetary.Amount
	for _, inv := range financialMap.Investments {
		totalInvestments += inv.CurrentValue
	}
	financialMap.TotalInvestments = totalInvestments

	// Update financial map
	if err := financialMap.PrepareUpdate(); err != nil {
		return nil, err
	}

	if err := s.Repository.UpdateFinancialMap(ctx, financialMap); err != nil {
		return nil, err
	}

	// Treat inner object ID
	investment.ID = investment.ObjectID.Hex()

	return investment, nil
}

func (s *service) FindInvestments(ctx context.Context, userID string) ([]*dashboard.Investment, error) {
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Convert slice to pointer slice
	result := make([]*dashboard.Investment, len(financialMap.Investments))
	for i := range financialMap.Investments {
		result[i] = financialMap.Investments[i]
		result[i].ID = result[i].ObjectID.Hex() // Treat inner object ID
	}

	return result, nil
}

func (s *service) UpdateInvestment(ctx context.Context, userID string, id string, name string, currentValue monetary.Amount) error {
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil {
		return err
	}

	// Find and update the specific investment
	found := false
	for i := range financialMap.Investments {
		if financialMap.Investments[i].ObjectID.Hex() == id {
			financialMap.Investments[i].Name = name
			financialMap.Investments[i].CurrentValue = currentValue
			if err := financialMap.Investments[i].PrepareUpdate(); err != nil {
				return err
			}
			found = true
			break
		}
	}

	if !found {
		return errors.New(errors.Service, "investment not found", errors.NotFound, nil)
	}

	// Recalculate total investments
	var totalInvestments monetary.Amount
	for _, inv := range financialMap.Investments {
		totalInvestments += inv.CurrentValue
	}
	financialMap.TotalInvestments = totalInvestments

	// Update financial map
	if err := financialMap.PrepareUpdate(); err != nil {
		return err
	}

	return s.Repository.UpdateFinancialMap(ctx, financialMap)
}

func (s *service) DeleteInvestment(ctx context.Context, userID string, id string) error {
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil {
		return err
	}

	// Find and remove the specific investment
	found := false
	for i := range financialMap.Investments {
		if financialMap.Investments[i].ObjectID.Hex() == id {
			financialMap.Investments = append(financialMap.Investments[:i], financialMap.Investments[i+1:]...)
			found = true
			break
		}
	}

	if !found {
		return errors.New(errors.Service, "investment not found", errors.NotFound, nil)
	}

	// Recalculate total investments
	var totalInvestments monetary.Amount
	for _, inv := range financialMap.Investments {
		totalInvestments += inv.CurrentValue
	}
	financialMap.TotalInvestments = totalInvestments

	// Update financial map
	if err := financialMap.PrepareUpdate(); err != nil {
		return err
	}

	return s.Repository.UpdateFinancialMap(ctx, financialMap)
}

// Asset operations
func (s *service) CreateAsset(ctx context.Context, userID string, description string, value monetary.Amount) (*dashboard.Asset, error) {
	// Get or create financial map
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil && !isNotFoundError(err) {
		return nil, err
	}

	if financialMap == nil {
		// Create new financial map if it doesn't exist
		financialMap, err = s.createNewFinancialMap(ctx, userID)
		if err != nil {
			return nil, err
		}
	}

	// Create new asset
	asset := &dashboard.Asset{
		ObjectID:     primitive.NewObjectID(), // Generate unique ObjectID for embedded document
		UserID:       userID,
		Name:         description,
		CurrentValue: value,
	}

	if err := asset.PrepareCreate(); err != nil {
		return nil, err
	}

	// Add to financial map
	financialMap.Assets = append(financialMap.Assets, asset)

	// Recalculate total assets
	var totalAssets monetary.Amount
	for _, a := range financialMap.Assets {
		totalAssets += a.CurrentValue
	}
	financialMap.TotalAssets = totalAssets

	// Update financial map
	if err := financialMap.PrepareUpdate(); err != nil {
		return nil, err
	}

	if err := s.Repository.UpdateFinancialMap(ctx, financialMap); err != nil {
		return nil, err
	}

	asset.ID = asset.ObjectID.Hex() // Treat inner object ID

	return asset, nil
}

func (s *service) FindAssets(ctx context.Context, userID string) ([]*dashboard.Asset, error) {
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil {
		if isNotFoundError(err) {
			return []*dashboard.Asset{}, nil // Return empty slice if no financial map exists
		}
		return nil, err
	}

	// Convert slice to pointer slice
	result := make([]*dashboard.Asset, len(financialMap.Assets))
	for i := range financialMap.Assets {
		result[i] = financialMap.Assets[i]
		result[i].ID = result[i].ObjectID.Hex() // Treat inner object ID
	}

	return result, nil
}

func (s *service) UpdateAsset(ctx context.Context, userID string, id string, description string, value monetary.Amount) error {
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil {
		return err
	}

	// Find and update the specific asset
	found := false
	for i := range financialMap.Assets {
		if financialMap.Assets[i].ObjectID.Hex() == id {
			financialMap.Assets[i].Name = description
			financialMap.Assets[i].CurrentValue = value
			if err := financialMap.Assets[i].PrepareUpdate(); err != nil {
				return err
			}
			found = true
			break
		}
	}

	if !found {
		return errors.New(errors.Service, "asset not found", errors.NotFound, nil)
	}

	// Recalculate total assets
	var totalAssets monetary.Amount
	for _, a := range financialMap.Assets {
		totalAssets += a.CurrentValue
	}
	financialMap.TotalAssets = totalAssets

	// Update financial map
	if err := financialMap.PrepareUpdate(); err != nil {
		return err
	}

	return s.Repository.UpdateFinancialMap(ctx, financialMap)
}

func (s *service) DeleteAsset(ctx context.Context, userID string, id string) error {
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil {
		return err
	}

	// Find and remove the specific asset
	found := false
	for i := range financialMap.Assets {
		if financialMap.Assets[i].ObjectID.Hex() == id {
			financialMap.Assets = append(financialMap.Assets[:i], financialMap.Assets[i+1:]...)
			found = true
			break
		}
	}

	if !found {
		return errors.New(errors.Service, "asset not found", errors.NotFound, nil)
	}

	// Recalculate total assets
	var totalAssets monetary.Amount
	for _, a := range financialMap.Assets {
		totalAssets += a.CurrentValue
	}
	financialMap.TotalAssets = totalAssets

	// Update financial map
	if err := financialMap.PrepareUpdate(); err != nil {
		return err
	}

	return s.Repository.UpdateFinancialMap(ctx, financialMap)
}

// Snapshot operations
// The method signature now accepts the two key time parameters from the scheduler.
func (s *service) CreateMonthlySnapshot(ctx context.Context, userID string, snapshotDate time.Time, creationDate time.Time) error {
	// Get financial map
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil {
		if isNotFoundError(err) {
			// No financial map exists, nothing to snapshot. This is a valid state.
			return nil
		}
		return err // Return other, unexpected errors.
	}

	// Calculate current values (This logic remains the same)
	var strategicFundValue monetary.Amount
	if financialMap.StrategicFund != nil {
		strategicFundValue = financialMap.StrategicFund.CurrentValue
	}
	totalValue := strategicFundValue + financialMap.TotalInvestments + financialMap.TotalAssets

	// --- CHANGED ---
	// Create snapshot using the parameters passed in by the scheduler.
	snapshot := &dashboard.NetWorthSnapshot{
		ObjectID:           primitive.NewObjectID(),
		UserID:             userID,
		Date:               snapshotDate, // Use the identifier date from the scheduler.
		StrategicFundValue: strategicFundValue,
		InvestmentsValue:   financialMap.TotalInvestments,
		AssetsValue:        financialMap.TotalAssets,
		TotalValue:         totalValue,
		CreatedAt:          creationDate, // Use the creation timestamp from the scheduler.
	}

	// Add snapshot to financial map history (limit to 12 months)
	financialMap.NetWorthHistory = append(financialMap.NetWorthHistory, snapshot)
	if len(financialMap.NetWorthHistory) > 12 {
		financialMap.NetWorthHistory = financialMap.NetWorthHistory[len(financialMap.NetWorthHistory)-12:]
	}

	// Update financial map
	if err := financialMap.PrepareUpdate(); err != nil {
		return err
	}

	return s.Repository.UpdateFinancialMap(ctx, financialMap)
}

func (s *service) FindNetWorthHistory(ctx context.Context, userID string, limit int) ([]*dashboard.NetWorthSnapshot, error) {
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil {
		if isNotFoundError(err) {
			return []*dashboard.NetWorthSnapshot{}, nil // Return empty slice if no financial map exists
		}
		return nil, err
	}

	// Get the requested number of snapshots (most recent first)
	history := financialMap.NetWorthHistory
	if len(history) > limit {
		history = history[len(history)-limit:]
	}

	// Convert slice to pointer slice
	result := make([]*dashboard.NetWorthSnapshot, len(history))
	for i := range history {
		result[i] = history[i]
	}

	return result, nil
}

// FindLatestNetWorthSnapshot returns the most recent snapshot's identifying date from the database.
func (s *service) FindLatestNetWorthSnapshot(ctx context.Context) (time.Time, error) {
	latestDate, err := s.Repository.FindLatestNetWorthSnapshot(ctx)
	if err != nil {
		// If the error is 'mongo.ErrNoDocuments', it means no snapshots were found.
		// The scheduler treats this as a valid state, not a fatal error, so we pass it along.
		if err == mongo.ErrNoDocuments {
			return time.Time{}, mongo.ErrNoDocuments
		}
		// For any other error, we wrap it.
		return time.Time{}, err
	}
	return latestDate, nil
}

// Helper
// isNotFoundError checks if the error is a NotFound error
func isNotFoundError(err error) bool {
	if domainErr, ok := err.(*errors.DomainError); ok {
		return domainErr.Kind() == errors.NotFound
	}
	return false
}

// isIncomeSourceModifiable checks if an income source can be modified (updated or deleted)
// Income sources are modifiable only if they are not blocked (Blocked = false)
// Blocked income sources originate from financial sheet service and are read-only
func isIncomeSourceModifiable(incomeSource *dashboard.IncomeSource) bool {
	return !incomeSource.Blocked
}
