package repository

import (
	"sync"

	"github.com/dsoplabs/dinbora-backend/internal/repository/billing/contract"
	"github.com/dsoplabs/dinbora-backend/internal/repository/billing/invoice"
	"github.com/dsoplabs/dinbora-backend/internal/repository/billing/product"
	achievement "github.com/dsoplabs/dinbora-backend/internal/repository/content/achievement"
	"github.com/dsoplabs/dinbora-backend/internal/repository/content/investmentcategory"
	"github.com/dsoplabs/dinbora-backend/internal/repository/content/ticker"
	"github.com/dsoplabs/dinbora-backend/internal/repository/content/trail"
	"github.com/dsoplabs/dinbora-backend/internal/repository/content/wallet"
	"github.com/dsoplabs/dinbora-backend/internal/repository/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/repository/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/repository/financialdna"
	"github.com/dsoplabs/dinbora-backend/internal/repository/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/repository/gamification"
	"github.com/dsoplabs/dinbora-backend/internal/repository/league" // Added league repository import
	"github.com/dsoplabs/dinbora-backend/internal/repository/progression"
	"github.com/dsoplabs/dinbora-backend/internal/repository/stripe/stripecustomer"
	"github.com/dsoplabs/dinbora-backend/internal/repository/stripe/stripeinvoice"
	"github.com/dsoplabs/dinbora-backend/internal/repository/stripe/stripeprice"
	"github.com/dsoplabs/dinbora-backend/internal/repository/stripe/stripeproduct"
	"github.com/dsoplabs/dinbora-backend/internal/repository/stripe/striperefund"
	"github.com/dsoplabs/dinbora-backend/internal/repository/stripe/stripesubscription"
	"github.com/dsoplabs/dinbora-backend/internal/repository/stripe/stripewebhook"
	"github.com/dsoplabs/dinbora-backend/internal/repository/user"
	"github.com/dsoplabs/dinbora-backend/internal/repository/vault"
	"github.com/dsoplabs/dinbora-backend/internal/repository/webhook"
	"github.com/stripe/stripe-go/v72/client"
	"go.mongodb.org/mongo-driver/mongo"
)

type RepositoryRegistry struct {
	// Billing
	Contract contract.Repository
	Invoice  invoice.Repository
	Product  product.Repository

	// Content
	Achievement         achievement.Repository
	Investimentcategory investmentcategory.Repository
	Ticker              ticker.Repository
	Trail               trail.Repository
	Wallet              wallet.Repository

	Dashboard  dashboard.Repository
	Dreamboard dreamboard.Repository

	FinancialDNA   financialdna.Repository
	FinancialSheet financialsheet.Repository
	Gamification   gamification.Repository
	League         league.Repository // Added League repository

	Progression progression.Repository

	// Stripe
	StripeCustomer     stripecustomer.Repository
	StripeInvoice      stripeinvoice.Repository
	StripePrice        stripeprice.Repository
	StripeProduct      stripeproduct.Repository
	StripeRefund       striperefund.Repository
	StripeSubscription stripesubscription.Repository
	StripeWebhook      stripewebhook.Repository

	User    user.Repository
	Vault   vault.Repository
	Webhook webhook.Repository
}

type RepositoryContainer struct {
	repositories map[string]interface{}
	db           *mongo.Database
	stripeClient *client.API
	mu           sync.RWMutex
}

func NewContainer(db *mongo.Database, stripeClient *client.API) *RepositoryContainer {
	return &RepositoryContainer{
		repositories: make(map[string]interface{}),
		db:           db,
		stripeClient: stripeClient,
	}
}

func (rc *RepositoryContainer) Register(name string, repo interface{}) {
	rc.mu.Lock()
	defer rc.mu.Unlock()
	rc.repositories[name] = repo
}

func (rc *RepositoryContainer) Get(name string) interface{} {
	rc.mu.RLock()
	defer rc.mu.RUnlock()
	return rc.repositories[name]
}

func (rc *RepositoryContainer) Initialize() *RepositoryRegistry {
	// Lazy initialization of repositories
	lazyInitRepo := func(name string, initFunc func() interface{}) interface{} {
		if existing := rc.Get(name); existing != nil {
			return existing
		}
		repo := initFunc()
		rc.Register(name, repo)
		return repo
	}

	// Initialize all repositories with the database
	// Billing
	contractRepo := lazyInitRepo("contract", func() interface{} {
		return contract.New(rc.db)
	}).(contract.Repository)
	invoiceRepo := lazyInitRepo("invoice", func() interface{} {
		return invoice.New(rc.db)
	}).(invoice.Repository)
	productRepo := lazyInitRepo("product", func() interface{} {
		return product.New(rc.db)
	}).(product.Repository)

	// Content
	achievementRepo := lazyInitRepo("achievement", func() interface{} {
		return achievement.New(rc.db)
	}).(achievement.Repository)
	investmentCategoryRepo := lazyInitRepo("investmentcategory", func() interface{} {
		return investmentcategory.New(rc.db)
	}).(investmentcategory.Repository)
	tickerRepo := lazyInitRepo("ticker", func() interface{} {
		return ticker.New(rc.db)
	}).(ticker.Repository)
	trailRepo := lazyInitRepo("trail", func() interface{} {
		return trail.New(rc.db)
	}).(trail.Repository)
	walletRepo := lazyInitRepo("wallet", func() interface{} {
		return wallet.New(rc.db)
	}).(wallet.Repository)

	dashboardRepo := lazyInitRepo("dashboard", func() interface{} {
		return dashboard.New(rc.db)
	}).(dashboard.Repository)
	dreamboardRepo := lazyInitRepo("dreamboard", func() interface{} {
		return dreamboard.New(rc.db)
	}).(dreamboard.Repository)

	financialDNARepo := lazyInitRepo("financialdna", func() interface{} {
		return financialdna.New(rc.db)
	}).(financialdna.Repository)

	financialSheetRepo := lazyInitRepo("financialsheet", func() interface{} {
		return financialsheet.New(rc.db)
	}).(financialsheet.Repository)

	gamificationRepo := lazyInitRepo("gamification", func() interface{} {
		return gamification.New(rc.db)
	}).(gamification.Repository)

	progressionRepo := lazyInitRepo("progression", func() interface{} {
		return progression.New(rc.db)
	}).(progression.Repository)

	// Stripe
	stripeCustomerRepo := lazyInitRepo("stripecustomer", func() interface{} {
		return stripecustomer.New(rc.stripeClient)
	}).(stripecustomer.Repository)
	stripeInvoiceRepo := lazyInitRepo("stripeinvoice", func() interface{} {
		return stripeinvoice.New(rc.stripeClient)
	}).(stripeinvoice.Repository)
	stripePriceRepo := lazyInitRepo("stripeprice", func() interface{} {
		return stripeprice.New(rc.stripeClient)
	}).(stripeprice.Repository)
	stripeProductRepo := lazyInitRepo("stripeproduct", func() interface{} {
		return stripeproduct.New(rc.stripeClient, stripePriceRepo)
	}).(stripeproduct.Repository)
	stripeRefundRepo := lazyInitRepo("striperefund", func() interface{} {
		return striperefund.New(rc.stripeClient)
	}).(striperefund.Repository)
	stripeSubscriptionRepo := lazyInitRepo("stripesubscription", func() interface{} {
		return stripesubscription.New(rc.stripeClient)
	}).(stripesubscription.Repository)
	stripeWebhookRepo := lazyInitRepo("stripewebhook", func() interface{} {
		return stripewebhook.New(rc.stripeClient)
	}).(stripewebhook.Repository)

	webhookRepo := lazyInitRepo("webhook", func() interface{} {
		return webhook.New(rc.db)
	}).(webhook.Repository)
	userRepo := lazyInitRepo("user", func() interface{} {
		return user.New(rc.db)
	}).(user.Repository)
	vaultRepo := lazyInitRepo("vault", func() interface{} {
		return vault.New(rc.db)
	}).(vault.Repository)
	leagueRepo := lazyInitRepo("league", func() interface{} {
		return league.New(rc.db) // Corrected constructor to package.New
	}).(league.Repository)

	return &RepositoryRegistry{
		Contract:            contractRepo,
		Invoice:             invoiceRepo,
		Product:             productRepo,
		Achievement:         achievementRepo,
		Investimentcategory: investmentCategoryRepo,
		Ticker:              tickerRepo,
		Trail:               trailRepo,
		Wallet:              walletRepo,
		Dashboard:           dashboardRepo,
		Dreamboard:          dreamboardRepo,
		FinancialDNA:        financialDNARepo,
		FinancialSheet:      financialSheetRepo,
		Gamification:        gamificationRepo,
		Progression:         progressionRepo,
		StripeCustomer:      stripeCustomerRepo,
		StripeInvoice:       stripeInvoiceRepo,
		StripePrice:         stripePriceRepo,
		StripeProduct:       stripeProductRepo,
		StripeRefund:        stripeRefundRepo,
		StripeSubscription:  stripeSubscriptionRepo,
		StripeWebhook:       stripeWebhookRepo,
		Webhook:             webhookRepo,
		User:                userRepo,
		Vault:               vaultRepo,
		League:              leagueRepo, // Added League repository to registry
	}
}
