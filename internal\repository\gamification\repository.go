package gamification

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/gamification"
)

type Reader interface {
	// Find user achievement by ID
	Find(ctx context.Context, id string) (*gamification.UserAchievement, error)
	
	// Find all achievements for a user
	FindByUser(ctx context.Context, userID string) ([]*gamification.UserAchievement, error)
	
	// Check if user already has a specific achievement
	HasAchievement(ctx context.Context, userID, achievementID string) (bool, error)
}

type Writer interface {
	// Create a new user achievement
	Create(ctx context.Context, userAchievement *gamification.UserAchievement) error
	
	// Update an existing user achievement
	Update(ctx context.Context, userAchievement *gamification.UserAchievement) error
	
	// Delete a user achievement
	Delete(ctx context.Context, id string) error
}

type Repository interface {
	Reader
	Writer
}
