package content

import (
	"strings"
	"time"

	"github.com/imdario/mergo"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Achievement struct {
	ObjectID    primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID          string             `json:"_id,omitempty" bson:"-"`
	Name        string             `json:"name" bson:"name"`
	Identifier  string             `json:"identifier" bson:"identifier"`
	Requirement string             `json:"requirement" bson:"requirement"`
	Level       uint8              `json:"level" bson:"level"`
	Logo        string             `json:"logo" bson:"logo"`
	CreatedAt   time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt   time.Time          `json:"updatedAt" bson:"updatedAt"`
	Conquered   bool               `json:"conquered" bson:"conquered"`
}

func (a *Achievement) PrepareCreate() error {
	a.Name = strings.TrimSpace(a.Name)
	a.Identifier = strings.TrimSpace(strings.ToLower(a.Identifier))
	a.Requirement = strings.TrimSpace(a.Requirement)
	a.Logo = strings.TrimSpace(a.Logo)

	a.CreatedAt = time.Now()
	a.UpdatedAt = a.CreatedAt

	return a.ValidateCreate()
}

func (a *Achievement) ValidateCreate() error {
	if a.Name == "" {
		return ErrAchievementRequiredName
	}

	if a.Identifier == "" {
		return ErrAchievementRequiredIdentifier
	}

	if a.Requirement == "" {
		return ErrAchievementRequiredRequirement
	}

	return nil
}

func (a *Achievement) PrepareUpdate(newAchievement *Achievement) error {
	if err := mergo.Merge(a, newAchievement, mergo.WithOverride); err != nil {
		return err
	}

	a.UpdatedAt = time.Now()

	return a.ValidateUpdate()
}

func (a *Achievement) ValidateUpdate() error {

	if a.ID == "" {
		if !a.ObjectID.IsZero() {
			a.ID = a.ObjectID.Hex()
		} else {
			return ErrAchievementInvalidID
		}
	} else {
		achievementObjectId, err := primitive.ObjectIDFromHex(a.ID)
		if err != nil {
			return err
		}
		a.ObjectID = achievementObjectId
	}

	return a.ValidateCreate()
}

func (a *Achievement) Sanitize() *Achievement {
	a.ID = a.ObjectID.Hex()

	return a
}
