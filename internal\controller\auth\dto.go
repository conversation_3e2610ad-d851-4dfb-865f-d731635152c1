package auth

type LoginResponseDTO struct {
	ID           string `json:"id"`
	Name         string `json:"name"`
	LastName     string `json:"lastName"`
	Email        string `json:"email"`
	PhotoURL     string `json:"photo"`
	ReferralCode string `json:"referralCode"`
	Wallet       string `json:"wallet"`
	Access       string `json:"access"`
	Refresh      string `json:"refresh"`
}

type RefreshResponseDTO struct {
	ID           string `json:"id"`
	Name         string `json:"name"`
	LastName     string `json:"lastName"`
	Email        string `json:"email"`
	PhotoURL     string `json:"photo"`
	ReferralCode string `json:"referralCode"`
	Wallet       string `json:"wallet"`
	Access       string `json:"access"`
	Refresh      string `json:"refresh"`
}
