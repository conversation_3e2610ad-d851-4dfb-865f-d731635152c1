package gamification

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/financialdna"
	"github.com/dsoplabs/dinbora-backend/internal/repository/content/achievement"
	_financialdna "github.com/dsoplabs/dinbora-backend/internal/repository/financialdna"
	_progression "github.com/dsoplabs/dinbora-backend/internal/repository/progression"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Adapter implementations to bridge existing repositories with gamification service

type ProgressionRepositoryAdapter struct {
	repo _progression.Repository
}

func NewProgressionRepositoryAdapter(repo _progression.Repository) ProgressionRepository {
	return &ProgressionRepositoryAdapter{repo: repo}
}

func (p *ProgressionRepositoryAdapter) FindByUser(ctx context.Context, userId string) (*ProgressionData, error) {
	prog, err := p.repo.FindByUser(ctx, userId)
	if err != nil {
		return nil, err
	}

	// Convert to gamification data structure
	data := &ProgressionData{
		UserID: prog.User,
		Trails: make([]*TrailData, len(prog.Trails)),
	}

	for i, trail := range prog.Trails {
		trailData := &TrailData{
			ID: trail.ID,
		}

		if trail.Challenge != nil {
			challengeData := &ChallengeData{
				Identifier: trail.Challenge.Identifier,
				Phases:     make([]*ChallengePhaseData, len(trail.Challenge.Phases)),
			}

			for j, phase := range trail.Challenge.Phases {
				challengeData.Phases[j] = &ChallengePhaseData{
					Identifier: phase.Identifier,
					Completed:  phase.Completed,
				}
			}

			trailData.Challenge = challengeData
		}

		data.Trails[i] = trailData
	}

	return data, nil
}

type FinancialDNARepositoryAdapter struct {
	repo _financialdna.Repository
}

func NewFinancialDNARepositoryAdapter(repo _financialdna.Repository) FinancialDNARepository {
	return &FinancialDNARepositoryAdapter{repo: repo}
}

func (f *FinancialDNARepositoryAdapter) FindByUser(ctx context.Context, userID string) (*FinancialDNAData, error) {
	// Convert userID string to ObjectID
	userObjectID, err := primitive.ObjectIDFromHex(userID)
	if err != nil {
		return nil, err
	}

	tree, err := f.repo.FindByUser(ctx, userObjectID)
	if err != nil {
		return nil, err
	}

	// Convert to gamification data structure
	data := &FinancialDNAData{
		UserID:  tree.UserID,
		Members: make([]*FamilyMemberData, len(tree.Members)),
	}

	for i, member := range tree.Members {
		memberData := &FamilyMemberData{
			ID:   member.ID,
			Name: member.Name,
		}

		// Check if financial status is populated
		if f.hasFinancialStatus(member.FinancialStatus) {
			memberData.FinancialStatus = &FinancialStatusData{
				IsPopulated: true,
			}
		}

		data.Members[i] = memberData
	}

	return data, nil
}

func (f *FinancialDNARepositoryAdapter) hasFinancialStatus(status financialdna.FinancialStatus) bool {
	// Check if financial status is populated (not undefined)
	return status != financialdna.FinancialStatusUndefined && status != ""
}

type AchievementRepositoryAdapter struct {
	repo achievement.Repository
}

func NewAchievementRepositoryAdapter(repo achievement.Repository) AchievementRepository {
	return &AchievementRepositoryAdapter{repo: repo}
}

func (a *AchievementRepositoryAdapter) FindByIdentifier(ctx context.Context, identifier string) (*AchievementData, error) {
	achievement, err := a.repo.FindByIdentifier(ctx, identifier)
	if err != nil {
		return nil, err
	}

	return &AchievementData{
		ID:         achievement.ID,
		Identifier: achievement.Identifier,
		Name:       achievement.Name,
	}, nil
}
