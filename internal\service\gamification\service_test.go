package gamification

import (
	"context"
	"testing"

	"github.com/dsoplabs/dinbora-backend/internal/model/gamification"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// Mock implementations for testing
type MockRepository struct {
	mock.Mock
}

func (m *MockRepository) Find(ctx context.Context, id string) (*gamification.UserAchievement, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*gamification.UserAchievement), args.Error(1)
}

func (m *MockRepository) FindByUser(ctx context.Context, userID string) ([]*gamification.UserAchievement, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]*gamification.UserAchievement), args.Error(1)
}

func (m *MockRepository) HasAchievement(ctx context.Context, userID, achievementID string) (bool, error) {
	args := m.Called(ctx, userID, achievementID)
	return args.Bool(0), args.Error(1)
}

func (m *MockRepository) Create(ctx context.Context, userAchievement *gamification.UserAchievement) error {
	args := m.Called(ctx, userAchievement)
	return args.Error(0)
}

func (m *MockRepository) Update(ctx context.Context, userAchievement *gamification.UserAchievement) error {
	args := m.Called(ctx, userAchievement)
	return args.Error(0)
}

func (m *MockRepository) Delete(ctx context.Context, id string) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

type MockProgressionRepository struct {
	mock.Mock
}

func (m *MockProgressionRepository) FindByUser(ctx context.Context, userId string) (*ProgressionData, error) {
	args := m.Called(ctx, userId)
	return args.Get(0).(*ProgressionData), args.Error(1)
}

type MockFinancialDNARepository struct {
	mock.Mock
}

func (m *MockFinancialDNARepository) FindByUser(ctx context.Context, userID string) (*FinancialDNAData, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).(*FinancialDNAData), args.Error(1)
}

type MockAchievementRepository struct {
	mock.Mock
}

func (m *MockAchievementRepository) FindByIdentifier(ctx context.Context, identifier string) (*AchievementData, error) {
	args := m.Called(ctx, identifier)
	return args.Get(0).(*AchievementData), args.Error(1)
}

func TestCheckDNAAchievement_UserAlreadyHasAchievement(t *testing.T) {
	// Arrange
	ctx := context.Background()
	userID := "test-user-id"
	
	mockRepo := &MockRepository{}
	mockProgressionRepo := &MockProgressionRepository{}
	mockFinancialDNARepo := &MockFinancialDNARepository{}
	mockAchievementRepo := &MockAchievementRepository{}
	
	service := New(mockRepo, mockProgressionRepo, mockFinancialDNARepo, mockAchievementRepo)
	
	// User already has DNA achievement
	mockRepo.On("HasAchievement", ctx, userID, "DNA").Return(true, nil)
	
	// Act
	err := service.CheckDNAAchievement(ctx, userID)
	
	// Assert
	assert.NoError(t, err)
	mockRepo.AssertExpectations(t)
	
	// Should not call other repositories since user already has achievement
	mockProgressionRepo.AssertNotCalled(t, "FindByUser")
	mockFinancialDNARepo.AssertNotCalled(t, "FindByUser")
}

func TestCheckDNAAchievement_ChallengeNotCompleted(t *testing.T) {
	// Arrange
	ctx := context.Background()
	userID := "test-user-id"
	
	mockRepo := &MockRepository{}
	mockProgressionRepo := &MockProgressionRepository{}
	mockFinancialDNARepo := &MockFinancialDNARepository{}
	mockAchievementRepo := &MockAchievementRepository{}
	
	service := New(mockRepo, mockProgressionRepo, mockFinancialDNARepo, mockAchievementRepo)
	
	// User doesn't have DNA achievement
	mockRepo.On("HasAchievement", ctx, userID, "DNA").Return(false, nil)
	
	// User hasn't completed perfil-financeiro challenge
	progressionData := &ProgressionData{
		UserID: userID,
		Trails: []*TrailData{
			{
				ID: "trail1",
				Challenge: &ChallengeData{
					Identifier: "some-challenge",
					Phases: []*ChallengePhaseData{
						{
							Identifier: "other-phase",
							Completed:  true,
						},
					},
				},
			},
		},
	}
	mockProgressionRepo.On("FindByUser", ctx, userID).Return(progressionData, nil)
	
	// Act
	err := service.CheckDNAAchievement(ctx, userID)
	
	// Assert
	assert.NoError(t, err)
	mockRepo.AssertExpectations(t)
	mockProgressionRepo.AssertExpectations(t)
	
	// Should not call financial DNA repo since challenge not completed
	mockFinancialDNARepo.AssertNotCalled(t, "FindByUser")
	mockRepo.AssertNotCalled(t, "Create")
}

func TestCheckDNAAchievement_SuccessfulAward(t *testing.T) {
	// Arrange
	ctx := context.Background()
	userID := "test-user-id"
	
	mockRepo := &MockRepository{}
	mockProgressionRepo := &MockProgressionRepository{}
	mockFinancialDNARepo := &MockFinancialDNARepository{}
	mockAchievementRepo := &MockAchievementRepository{}
	
	service := New(mockRepo, mockProgressionRepo, mockFinancialDNARepo, mockAchievementRepo)
	
	// User doesn't have DNA achievement
	mockRepo.On("HasAchievement", ctx, userID, "DNA").Return(false, nil)
	
	// User has completed perfil-financeiro challenge
	progressionData := &ProgressionData{
		UserID: userID,
		Trails: []*TrailData{
			{
				ID: "trail1",
				Challenge: &ChallengeData{
					Identifier: "some-challenge",
					Phases: []*ChallengePhaseData{
						{
							Identifier: "perfil-financeiro",
							Completed:  true,
						},
					},
				},
			},
		},
	}
	mockProgressionRepo.On("FindByUser", ctx, userID).Return(progressionData, nil)
	
	// User has completed financial DNA
	financialDNAData := &FinancialDNAData{
		UserID: userID,
		Members: []*FamilyMemberData{
			{
				ID:   "father",
				Name: "father",
				FinancialStatus: &FinancialStatusData{
					IsPopulated: true,
				},
			},
			{
				ID:   "mother",
				Name: "mother",
				FinancialStatus: &FinancialStatusData{
					IsPopulated: true,
				},
			},
		},
	}
	mockFinancialDNARepo.On("FindByUser", ctx, userID).Return(financialDNAData, nil)
	
	// Mock achievement creation
	mockRepo.On("Create", ctx, mock.MatchedBy(func(ua *gamification.UserAchievement) bool {
		return ua.UserID == userID && ua.AchievementID == "DNA"
	})).Return(nil)
	
	// Act
	err := service.CheckDNAAchievement(ctx, userID)
	
	// Assert
	assert.NoError(t, err)
	mockRepo.AssertExpectations(t)
	mockProgressionRepo.AssertExpectations(t)
	mockFinancialDNARepo.AssertExpectations(t)
}
