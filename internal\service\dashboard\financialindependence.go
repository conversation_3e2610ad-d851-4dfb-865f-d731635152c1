package dashboard

import (
	"context"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	MONTHLY_INTEREST_RATE = 0.0065
)

// FindFinancialIndependence retrieves or calculates financial independence data for a user
func (s *service) FindFinancialIndependence(ctx context.Context, userID string) (*dashboard.FinancialIndependence, error) {
	// Try to get existing financial independence data
	existingFI, err := s.Repository.FindFinancialIndependenceByUser(ctx, userID)
	if err != nil && !isNotFoundError(err) {
		return nil, err
	}

	// Calculate live data
	monthlyInvestment, err := s.calculateMonthlyInvestmentContribution(ctx, userID)
	if err != nil {
		return nil, err
	}

	monthlyExpenses, err := s.calculateMonthlyExpenses(ctx, userID)
	if err != nil {
		return nil, err
	}

	currentNetWorth, investments, strategicFund, err := s.calculateCurrentNetWorth(ctx, userID) // without assets
	if err != nil {
		return nil, err
	}

	firstTransactionDate, err := s.findFirstTransactionDate(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Determine retirement target amount - use existing value or default to 0
	var retirementTargetAmount monetary.Amount
	if existingFI != nil {
		retirementTargetAmount = existingFI.RetirementTargetAmount
	} else {
		retirementTargetAmount = 0 // Default for new users
	}

	financialIndependenceData, err := s.calculateFinancialIndependenceData(retirementTargetAmount, monthlyInvestment, currentNetWorth)
	if err != nil {
		return nil, err
	}

	partialIndependence := &dashboard.PartialIndependence{
		CurrentValue: currentNetWorth,
		GoalValue:    financialIndependenceData.TargetAmount / 2,
	}

	fullIndependence := &dashboard.FullIndependence{
		CurrentValue: currentNetWorth,
		GoalValue:    financialIndependenceData.TargetAmount,
	}

	// If no existing data, create new with default retirement target
	if existingFI == nil {
		financialIndependence := &dashboard.FinancialIndependence{
			ObjectID:                  primitive.NewObjectID(),
			UserID:                    userID,
			RetirementTargetAmount:    0, // Default R$0 it indicate to front-end to force user calculation.
			MonthlyInvestment:         monthlyInvestment,
			MonthlyExpenses:           monthlyExpenses,
			CurrentNetWorth:           currentNetWorth,
			Investments:               investments,
			FinancialIndependenceData: financialIndependenceData,
			StrategicFund:             strategicFund,
			PartialIndependence:       partialIndependence,
			FullIndependence:          fullIndependence,
			FirstTransactionDate:      firstTransactionDate,
		}

		if err := financialIndependence.PrepareCreate(); err != nil {
			return nil, err
		}

		// Save to repository
		if err := s.Repository.CreateFinancialIndependence(ctx, financialIndependence); err != nil {
			return nil, err
		}

		financialIndependence.ID = financialIndependence.ObjectID.Hex()
		return financialIndependence, nil
	}

	// Update existing data with live calculations
	existingFI.MonthlyInvestment = monthlyInvestment
	existingFI.MonthlyExpenses = monthlyExpenses
	existingFI.CurrentNetWorth = currentNetWorth
	existingFI.Investments = investments
	existingFI.FinancialIndependenceData = financialIndependenceData
	existingFI.StrategicFund = strategicFund
	existingFI.PartialIndependence = partialIndependence
	existingFI.FullIndependence = fullIndependence
	existingFI.FirstTransactionDate = firstTransactionDate

	if err := existingFI.PrepareUpdate(); err != nil {
		return nil, err
	}

	// Save updated data to repository
	if err := s.Repository.UpdateFinancialIndependence(ctx, existingFI); err != nil {
		return nil, err
	}

	return existingFI, nil
}

// UpdateRetirementTargetAmount updates the user's retirement target amount
func (s *service) UpdateRetirementTargetAmount(ctx context.Context, userID string, targetAmount monetary.Amount) error {
	if targetAmount <= 0 {
		return errors.New(errors.Service, "retirement target amount must be positive", errors.Validation, nil)
	}

	// Get or create financial independence data
	financialIndependence, err := s.FindFinancialIndependence(ctx, userID)
	if err != nil {
		return err
	}

	// Update the target amount
	financialIndependence.RetirementTargetAmount = targetAmount

	if err := financialIndependence.PrepareUpdate(); err != nil {
		return err
	}

	// Save to repository
	return s.Repository.UpdateFinancialIndependence(ctx, financialIndependence)
}

// calculateMonthlyInvestmentContribution calculates average monthly investment contribution
// from personal_reserves category with "Investimentos" money source
func (s *service) calculateMonthlyInvestmentContribution(ctx context.Context, userID string) (monetary.Amount, error) {
	// Get all transactions without date filters to calculate overall average
	allTransactions, err := s.FinancialSheetService.FindAllTransactions(ctx, userID, "", 0, 0, false)
	if err != nil {
		return 0, err
	}

	var totalInvestmentContrib monetary.Amount
	var transactionCount int

	for _, transaction := range allTransactions {
		// Filter for personal_reserves category with "Investimentos" money source
		if transaction.Category == financialsheet.CategoryIdentifierPersonalReserves &&
			transaction.MoneySource == financialsheet.MoneySourceOpt4 { // MoneySourceOpt4 = "Investimentos"
			totalInvestmentContrib += transaction.Value
			transactionCount++
		}
	}

	if transactionCount == 0 {
		return 0, nil
	}

	// Calculate average per transaction, then estimate monthly average
	// This is a simplified approach - in a real scenario, you might want to group by month
	averagePerTransaction := totalInvestmentContrib / monetary.Amount(transactionCount)

	// Estimate monthly average (assuming transactions are somewhat evenly distributed)
	// This could be improved by calculating actual monthly averages
	return averagePerTransaction, nil
}

// calculateMonthlyExpenses calculates average monthly expenses excluding investment reserves
func (s *service) calculateMonthlyExpenses(ctx context.Context, userID string) (monetary.Amount, error) {
	// Get all expense transactions
	allTransactions, err := s.FinancialSheetService.FindAllTransactions(ctx, userID, "", 0, 0, false)
	if err != nil {
		return 0, err
	}

	var totalExpenses monetary.Amount
	var transactionCount int

	for _, transaction := range allTransactions {
		// Include costs_of_living and expense types, but exclude investment reserves
		if (transaction.Type == financialsheet.CategoryTypeCostsOfLiving ||
			transaction.Type == financialsheet.CategoryTypeExpense) &&
			!(transaction.Category == financialsheet.CategoryIdentifierPersonalReserves &&
				transaction.MoneySource == financialsheet.MoneySourceOpt4) { // Exclude investment reserves
			totalExpenses += transaction.Value
			transactionCount++
		}
	}

	if transactionCount == 0 {
		return 0, nil
	}

	// Calculate average per transaction
	averagePerTransaction := totalExpenses / monetary.Amount(transactionCount)

	return averagePerTransaction, nil
}

// calculateCurrentNetWorth calculates current net worth from financial map
func (s *service) calculateCurrentNetWorth(ctx context.Context, userID string) (monetary.Amount, monetary.Amount, *dashboard.StrategicFund, error) {
	financialMap, err := s.FindFinancialMap(ctx, userID)
	if err != nil {
		return 0, 0, nil, err
	}

	var strategicFundValue monetary.Amount
	if financialMap.StrategicFund != nil {
		strategicFundValue = financialMap.StrategicFund.CurrentValue
	}

	// Clear unnecessary fields from strategic fund
	if financialMap.StrategicFund != nil {
		financialMap.StrategicFund.ObjectID = primitive.ObjectID{}
		financialMap.StrategicFund.ID = ""
		financialMap.StrategicFund.UserID = ""
		financialMap.StrategicFund.CreatedAt = nil
		financialMap.StrategicFund.UpdatedAt = nil
	}

	// Removed financialMap.TotalAssets from the calculation
	totalNetWorth := strategicFundValue + financialMap.TotalInvestments
	return totalNetWorth, financialMap.TotalInvestments, financialMap.StrategicFund, nil
}

// findFirstTransactionDate finds the date of the user's first transaction
func (s *service) findFirstTransactionDate(ctx context.Context, userID string) (*time.Time, error) {
	// Get all transactions
	allTransactions, err := s.FinancialSheetService.FindAllTransactions(ctx, userID, "", 0, 0, false)
	if err != nil {
		return nil, err
	}

	if len(allTransactions) == 0 {
		return nil, nil
	}

	// Find the earliest transaction date
	var earliestDate time.Time
	for i, transaction := range allTransactions {
		if i == 0 || transaction.Date.Before(earliestDate) {
			earliestDate = transaction.Date
		}
	}

	return &earliestDate, nil
}

// calculateFinancialIndependenceData calculates the financial independence data
func (s *service) calculateFinancialIndependenceData(retirementTargetAmount monetary.Amount, monthlyInvestment monetary.Amount, currentNetWorth monetary.Amount) (*dashboard.FinancialIndependenceData, error) {
	// If no retirement target is set, return default values
	if retirementTargetAmount <= 0 {
		return &dashboard.FinancialIndependenceData{
			TargetDate:          nil,
			TargetAmount:        0,
			TargetPassiveIncome: 0,
		}, nil
	}

	// Calculate target amount needed for financial independence
	targetAmount := float64(retirementTargetAmount*2) / MONTHLY_INTEREST_RATE

	// If monthly investment is 0, we can't calculate time to target
	if monthlyInvestment <= 0 {
		return &dashboard.FinancialIndependenceData{
			TargetDate:          nil,
			TargetAmount:        monetary.Amount(targetAmount),
			TargetPassiveIncome: retirementTargetAmount * 2, // Target amount is twice the retirement target amount
		}, nil
	}

	// Calculate months to target using compound interest formula
	// Formula: FV = PV * (1 + r)^n + PMT * [((1 + r)^n - 1) / r]
	// Where: FV = Future Value (target), PV = Present Value (current net worth),
	//        PMT = Monthly Payment (monthly investment), r = monthly interest rate, n = number of months

	currentValue := float64(currentNetWorth)
	monthlyPayment := float64(monthlyInvestment)
	monthlyRate := MONTHLY_INTEREST_RATE

	// We need to solve for n in the compound interest formula
	// This requires iterative calculation or approximation
	monthsToTarget := calculateMonthsToTarget(currentValue, monthlyPayment, targetAmount, monthlyRate)

	var targetDate *time.Time
	if monthsToTarget > 0 {
		date := time.Now().AddDate(0, monthsToTarget, 0)
		targetDate = &date
	}

	return &dashboard.FinancialIndependenceData{
		TargetDate:          targetDate,
		TargetAmount:        monetary.Amount(targetAmount),
		TargetPassiveIncome: retirementTargetAmount * 2, // Target amount is twice the retirement target amount
	}, nil
}

// calculateMonthsToTarget calculates the number of months needed to reach the target amount
// using compound interest with monthly contributions
func calculateMonthsToTarget(currentValue, monthlyPayment, targetAmount, monthlyRate float64) int {
	if targetAmount <= currentValue {
		return 0 // Already reached the target
	}

	if monthlyPayment <= 0 {
		return -1 // Cannot reach target without contributions
	}

	// Use iterative approach to find the number of months
	// This is more accurate than the logarithmic formula for this use case
	balance := currentValue
	months := 0
	maxMonths := 1200 // Cap at 100 years to prevent infinite loops

	for balance < targetAmount && months < maxMonths {
		// Apply monthly interest to current balance
		balance = balance * (1 + monthlyRate)
		// Add monthly contribution
		balance += monthlyPayment
		months++
	}

	if months >= maxMonths {
		return -1 // Target not reachable within reasonable time
	}

	return months
}
