package controller

import (
	"context"
	"sync"

	"github.com/dsoplabs/dinbora-backend/internal/api/service"
	"github.com/dsoplabs/dinbora-backend/internal/controller/aiassistant"
	"github.com/dsoplabs/dinbora-backend/internal/controller/apple"
	"github.com/dsoplabs/dinbora-backend/internal/controller/auth"
	"github.com/dsoplabs/dinbora-backend/internal/controller/billing/contract"
	"github.com/dsoplabs/dinbora-backend/internal/controller/billing/product"
	"github.com/dsoplabs/dinbora-backend/internal/controller/billing/subscription"
	achievement "github.com/dsoplabs/dinbora-backend/internal/controller/content/achievement"
	"github.com/dsoplabs/dinbora-backend/internal/controller/content/investmentcategory"
	"github.com/dsoplabs/dinbora-backend/internal/controller/content/ticker"
	"github.com/dsoplabs/dinbora-backend/internal/controller/content/trail"
	"github.com/dsoplabs/dinbora-backend/internal/controller/content/wallet"
	"github.com/dsoplabs/dinbora-backend/internal/controller/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/controller/dreamboard"
	"github.com/dsoplabs/dinbora-backend/internal/controller/financialdna"
	"github.com/dsoplabs/dinbora-backend/internal/controller/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/controller/google"
	"github.com/dsoplabs/dinbora-backend/internal/controller/league"
	"github.com/dsoplabs/dinbora-backend/internal/controller/notification/sendgrid"
	"github.com/dsoplabs/dinbora-backend/internal/controller/progression"
	"github.com/dsoplabs/dinbora-backend/internal/controller/user"
	"github.com/labstack/echo/v4"
)

type RouteHandler interface {
	RegisterRoutes(ctx context.Context, legacyGroup *echo.Group, currentGroup *echo.Group)
}

type ControllerRegistry struct {
	Apple apple.Controller
	Auth  auth.Controller

	// Billing
	Contract     contract.Controller
	Product      product.Controller
	Subscription subscription.Controller

	// Content
	Achievement        achievement.Controller
	InvestmentCategory investmentcategory.Controller
	Ticker             ticker.Controller
	Trail              trail.Controller
	Wallet             wallet.Controller

	Dashboard      dashboard.Controller
	Dreamboard     dreamboard.Controller
	FinancialDNA   financialdna.Controller
	FinancialSheet financialsheet.Controller
	Google         google.Controller
	League         league.Controller
	Sendgrid       sendgrid.Controller
	Progression    progression.Controller
	User           user.Controller

	// AI
	AIAssistant aiassistant.Controller

	// Admin
	UserAdmin user.AdminController
}

type ControllerContainer struct {
	controllers map[string]interface{}
	services    *service.ServiceRegistry
	mu          sync.RWMutex
}

func NewContainer(services *service.ServiceRegistry) *ControllerContainer {
	return &ControllerContainer{
		controllers: make(map[string]interface{}),
		services:    services,
	}
}

func (cc *ControllerContainer) Register(name string, controller interface{}) {
	cc.mu.Lock()
	defer cc.mu.Unlock()
	cc.controllers[name] = controller
}

func (cc *ControllerContainer) Get(name string) interface{} {
	cc.mu.RLock()
	defer cc.mu.RUnlock()
	return cc.controllers[name]
}

func (cc *ControllerContainer) Initialize() *ControllerRegistry {
	// Lazy initialization of Services
	lazyInitService := func(name string, initFunc func() interface{}) interface{} {
		if existing := cc.Get(name); existing != nil {
			return existing
		}
		service := initFunc()
		cc.Register(name, service)
		return service
	}

	appleController := lazyInitService("apple", func() interface{} {
		return apple.New(cc.services.User, cc.services.S3)
	}).(apple.Controller)
	authController := lazyInitService("auth", func() interface{} {
		return auth.New(cc.services.Auth)
	}).(auth.Controller)
	// Billing
	contractController := lazyInitService("contract", func() interface{} {
		return contract.New(cc.services.Contract)
	}).(contract.Controller)
	productController := lazyInitService("product", func() interface{} {
		return product.New(cc.services.Product)
	}).(product.Controller)
	subscriptionController := lazyInitService("subscription", func() interface{} {
		return subscription.New(cc.services.Subscription, cc.services.Webhook)
	}).(subscription.Controller)

	// Content
	achievementController := lazyInitService("achievement", func() interface{} {
		return achievement.New(cc.services.Achievement)
	}).(achievement.Controller)
	investmentcategoryController := lazyInitService("investmentcategory", func() interface{} {
		return investmentcategory.New(cc.services.InvestmentCategory)
	}).(investmentcategory.Controller)
	tickerController := lazyInitService("ticker", func() interface{} {
		return ticker.New(cc.services.Ticker)
	}).(ticker.Controller)
	trailController := lazyInitService("trail", func() interface{} {
		return trail.New(cc.services.Trail, cc.services.User, cc.services.Contract)
	}).(trail.Controller)
	walletController := lazyInitService("wallet", func() interface{} {
		return wallet.New(cc.services.Wallet)
	}).(wallet.Controller)

	dashboardController := lazyInitService("dashboard", func() interface{} {
		return dashboard.New(cc.services.Dashboard)
	}).(dashboard.Controller)
	dreamboardController := lazyInitService("dreamboard", func() interface{} {
		return dreamboard.New(cc.services.Dreamboard, cc.services.User)
	}).(dreamboard.Controller)
	financialDNAController := lazyInitService("financialdna", func() interface{} {
		return financialdna.New(cc.services.FinancialDNA)
	}).(financialdna.Controller)
	financialsheetController := lazyInitService("financialsheet", func() interface{} {
		return financialsheet.New(cc.services.FinancialSheet)
	}).(financialsheet.Controller)
	googleController := lazyInitService("google", func() interface{} {
		return google.New(cc.services.User, cc.services.S3)
	}).(google.Controller)
	leagueController := lazyInitService("league", func() interface{} {
		return league.New(cc.services.League, cc.services.User)
	}).(league.Controller)
	sendgridController := lazyInitService("sendgrid", func() interface{} {
		return sendgrid.New()
	}).(sendgrid.Controller)
	progressionController := lazyInitService("progression", func() interface{} {
		return progression.New(cc.services.Progression, cc.services.User, cc.services.Contract)
	}).(progression.Controller)
	userController := lazyInitService("user", func() interface{} {
		return user.New(cc.services.User, cc.services.S3)
	}).(user.Controller)

	// Admin
	userAdminController := lazyInitService("userAdmin", func() interface{} {
		return user.NewAdmin(cc.services.User, cc.services.S3)
	}).(user.AdminController)

	// AI
	aiAssistantController := lazyInitService("aiassistant", func() interface{} {
		return aiassistant.New(cc.services.AIAssistant)
	}).(aiassistant.Controller)

	return &ControllerRegistry{
		Apple:              appleController,
		Auth:               authController,
		Contract:           contractController,
		Product:            productController,
		Subscription:       subscriptionController,
		Achievement:        achievementController,
		InvestmentCategory: investmentcategoryController,
		Ticker:             tickerController,
		Trail:              trailController,
		Wallet:             walletController,
		Dashboard:          dashboardController,
		Dreamboard:         dreamboardController,
		FinancialDNA:       financialDNAController,
		FinancialSheet:     financialsheetController,
		Google:             googleController,
		League:             leagueController,
		Sendgrid:           sendgridController,
		Progression:        progressionController,
		User:               userController,
		AIAssistant:        aiAssistantController,

		// Admin
		UserAdmin: userAdminController,
	}
}

func (cr *ControllerRegistry) GetAll() []RouteHandler {
	return []RouteHandler{
		cr.Apple,
		cr.Auth,
		cr.Contract,
		cr.Product,
		cr.Subscription,
		cr.Achievement,
		cr.InvestmentCategory,
		cr.Ticker,
		cr.Trail,
		cr.Wallet,
		cr.Dashboard,
		cr.Dreamboard,
		cr.FinancialDNA,
		cr.FinancialSheet,
		cr.Google,
		cr.League,
		cr.Sendgrid,
		cr.Progression,
		cr.User,
		cr.AIAssistant,

		// Admin
		cr.UserAdmin,
	}
}
