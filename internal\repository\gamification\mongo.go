package gamification

import (
	"context"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/gamification"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	collection *mongo.Collection
}

func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		collection: db.Collection(repository.GAMIFICATION_USER_ACHIEVEMENTS_COLLECTION),
	}

	// Create compound index on userID and achievementID for efficient lookups and uniqueness
	_, err := repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys: bson.D{
				{Key: "userId", Value: 1},
				{Key: "achievementId", Value: 1},
			},
			Options: options.Index().SetUnique(true).SetName("user_achievement_unique"),
		},
	)
	if err != nil {
		log.Println("warning: failed to create compound index on userId and achievementId fields")
	}

	// Create index on userID for efficient user queries
	_, err = repo.collection.Indexes().CreateOne(
		context.Background(),
		mongo.IndexModel{
			Keys:    bson.D{{Key: "userId", Value: 1}},
			Options: options.Index().SetName("user_achievements"),
		},
	)
	if err != nil {
		log.Println("warning: failed to create index on userId field")
	}

	return repo
}

// Reader implementation
func (m *mongoDB) Find(ctx context.Context, id string) (*gamification.UserAchievement, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New(errors.Repository, "invalid achievement ID format", errors.Validation, err)
	}

	filter := bson.D{{Key: "_id", Value: objectID}}
	
	var userAchievement gamification.UserAchievement
	err = m.collection.FindOne(ctx, filter).Decode(&userAchievement)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "user achievement not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find user achievement", errors.Internal, err)
	}

	userAchievement.SetID()
	return &userAchievement, nil
}

func (m *mongoDB) FindByUser(ctx context.Context, userID string) ([]*gamification.UserAchievement, error) {
	filter := bson.D{{Key: "userId", Value: userID}}
	
	// Sort by earnedAt descending (most recent first)
	opts := options.Find().SetSort(bson.D{{Key: "earnedAt", Value: -1}})
	
	cursor, err := m.collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, errors.New(errors.Repository, "failed to find user achievements", errors.Internal, err)
	}
	defer cursor.Close(ctx)

	var achievements []*gamification.UserAchievement
	for cursor.Next(ctx) {
		var achievement gamification.UserAchievement
		if err := cursor.Decode(&achievement); err != nil {
			return nil, errors.New(errors.Repository, "failed to decode user achievement", errors.Internal, err)
		}
		achievement.SetID()
		achievements = append(achievements, &achievement)
	}

	if err := cursor.Err(); err != nil {
		return nil, errors.New(errors.Repository, "cursor error while reading user achievements", errors.Internal, err)
	}

	return achievements, nil
}

func (m *mongoDB) HasAchievement(ctx context.Context, userID, achievementID string) (bool, error) {
	filter := bson.D{
		{Key: "userId", Value: userID},
		{Key: "achievementId", Value: achievementID},
	}
	
	count, err := m.collection.CountDocuments(ctx, filter)
	if err != nil {
		return false, errors.New(errors.Repository, "failed to check if user has achievement", errors.Internal, err)
	}
	
	return count > 0, nil
}

// Writer implementation
func (m *mongoDB) Create(ctx context.Context, userAchievement *gamification.UserAchievement) error {
	userAchievement.PrepareCreate()
	
	result, err := m.collection.InsertOne(ctx, userAchievement)
	if err != nil {
		if mongo.IsDuplicateKeyError(err) {
			return errors.New(errors.Repository, "user already has this achievement", errors.Conflict, err)
		}
		return errors.New(errors.Repository, "failed to create user achievement", errors.Internal, err)
	}

	if objectID, ok := result.InsertedID.(primitive.ObjectID); ok {
		userAchievement.ObjectID = objectID
		userAchievement.SetID()
	}

	return nil
}

func (m *mongoDB) Update(ctx context.Context, userAchievement *gamification.UserAchievement) error {
	userAchievement.PrepareUpdate()
	
	filter := bson.D{{Key: "_id", Value: userAchievement.ObjectID}}
	update := bson.D{{Key: "$set", Value: userAchievement}}
	
	result, err := m.collection.UpdateOne(ctx, filter, update)
	if err != nil {
		return errors.New(errors.Repository, "failed to update user achievement", errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, "user achievement not found for update", errors.NotFound, nil)
	}

	return nil
}

func (m *mongoDB) Delete(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Repository, "invalid achievement ID format", errors.Validation, err)
	}

	filter := bson.D{{Key: "_id", Value: objectID}}
	
	result, err := m.collection.DeleteOne(ctx, filter)
	if err != nil {
		return errors.New(errors.Repository, "failed to delete user achievement", errors.Internal, err)
	}

	if result.DeletedCount == 0 {
		return errors.New(errors.Repository, "user achievement not found for deletion", errors.NotFound, nil)
	}

	return nil
}
