package achievement

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/content"
	"github.com/dsoplabs/dinbora-backend/internal/repository/content/achievement"
)

type Service interface {
	// Achievement CRUD
	Create(ctx context.Context, achievement *content.Achievement) error
	Find(ctx context.Context, id string) (*content.Achievement, error)
	FindAll(ctx context.Context) ([]*content.Achievement, error)
	FindByIdentifier(ctx context.Context, identifier string) (*content.Achievement, error)
	FindByRequirement(ctx context.Context, requirement string) (*content.Achievement, error)
	Update(ctx context.Context, achievement *content.Achievement) error
	Delete(ctx context.Context, id string) error
}

type service struct {
	Repository achievement.Repository
}

func New(repository achievement.Repository) Service {
	return &service{
		Repository: repository,
	}
}

// CRUD
func (s *service) Create(ctx context.Context, achievement *content.Achievement) error {
	foundAchievement, err := s.Repository.FindByIdentifier(ctx, achievement.Identifier)
	if err == nil && foundAchievement != nil {
		return errors.New(errors.Service, "achievement already exists", errors.Conflict, err)
	}

	if err = s.Repository.Create(ctx, achievement); err != nil {
		return err
	}

	return nil
}

func (s *service) Find(ctx context.Context, id string) (*content.Achievement, error) {
	foundAchievement, err := s.Repository.Find(ctx, id)
	if err != nil {
		return nil, err
	}
	foundAchievement.ID = foundAchievement.ObjectID.Hex()
	return foundAchievement, nil
}

func (s *service) FindAll(ctx context.Context) ([]*content.Achievement, error) {
	foundAchievements, err := s.Repository.FindAll(ctx)
	if err != nil {
		return nil, err
	}
	for _, foundAchievement := range foundAchievements {
		foundAchievement.ID = foundAchievement.ObjectID.Hex()
	}

	return foundAchievements, nil
}

func (s *service) FindByIdentifier(ctx context.Context, identifier string) (*content.Achievement, error) {
	foundAchievement, err := s.Repository.FindByIdentifier(ctx, identifier)
	if err != nil {
		return nil, err
	}
	foundAchievement.ID = foundAchievement.ObjectID.Hex()
	return foundAchievement, nil
}

func (s *service) FindByRequirement(ctx context.Context, requirement string) (*content.Achievement, error) {
	foundAchievement, err := s.Repository.FindByRequirement(ctx, requirement)
	if err != nil {
		return nil, err
	}
	foundAchievement.ID = foundAchievement.ObjectID.Hex()
	return foundAchievement, nil
}

func (s *service) Update(ctx context.Context, achievement *content.Achievement) error {
	return s.Repository.Update(ctx, achievement)
}

func (s *service) Delete(ctx context.Context, id string) error {
	return s.Repository.Delete(ctx, id)
}
