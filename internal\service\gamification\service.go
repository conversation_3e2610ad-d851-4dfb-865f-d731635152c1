package gamification

import (
	"context"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/gamification"
)

type Service interface {
	// Achievement management
	GetUserAchievements(ctx context.Context, userID string) ([]*gamification.UserAchievement, error)

	// Event-driven achievement checking
	CheckAchievements(ctx context.Context, userID string) error

	// Specific achievement checks (can be called individually or as part of CheckAchievements)
	CheckDNAAchievement(ctx context.Context, userID string) error
}

type service struct {
	Repository       Repository
	ProgressionRepo  ProgressionRepository
	FinancialDNARepo FinancialDNARepository
	AchievementRepo  AchievementRepository
}

// Repository interfaces for dependencies
type Repository interface {
	Find(ctx context.Context, id string) (*gamification.UserAchievement, error)
	FindByUser(ctx context.Context, userID string) ([]*gamification.UserAchievement, error)
	HasAchievement(ctx context.Context, userID, achievementID string) (bool, error)
	Create(ctx context.Context, userAchievement *gamification.UserAchievement) error
	Update(ctx context.Context, userAchievement *gamification.UserAchievement) error
	Delete(ctx context.Context, id string) error
}

// ProgressionRepository interface for progression-related queries
type ProgressionRepository interface {
	FindByUser(ctx context.Context, userId string) (*ProgressionData, error)
}

// FinancialDNARepository interface for financial DNA queries
type FinancialDNARepository interface {
	FindByUser(ctx context.Context, userID string) (*FinancialDNAData, error)
}

// AchievementRepository interface for achievement content queries
type AchievementRepository interface {
	FindByIdentifier(ctx context.Context, identifier string) (*AchievementData, error)
}

// Data structures for repository responses (simplified for gamification needs)
type ProgressionData struct {
	UserID string
	Trails []*TrailData
}

type TrailData struct {
	ID        string
	Challenge *ChallengeData
}

type ChallengeData struct {
	Identifier string
	Phases     []*ChallengePhaseData
}

type ChallengePhaseData struct {
	Identifier string
	Completed  bool
}

type FinancialDNAData struct {
	UserID  string
	Members []*FamilyMemberData
}

type FamilyMemberData struct {
	ID              string
	Name            string
	FinancialStatus *FinancialStatusData
}

type FinancialStatusData struct {
	// This represents that the financial status is populated
	// The actual fields don't matter for gamification, just that it exists
	IsPopulated bool
}

type AchievementData struct {
	ID         string
	Identifier string
	Name       string
}

func New(repository Repository, progressionRepo ProgressionRepository, financialDNARepo FinancialDNARepository, achievementRepo AchievementRepository) Service {
	return &service{
		Repository:       repository,
		ProgressionRepo:  progressionRepo,
		FinancialDNARepo: financialDNARepo,
		AchievementRepo:  achievementRepo,
	}
}

// Achievement management
func (s *service) GetUserAchievements(ctx context.Context, userID string) ([]*gamification.UserAchievement, error) {
	achievements, err := s.Repository.FindByUser(ctx, userID)
	if err != nil {
		return nil, errors.New(errors.Service, "failed to get user achievements", errors.Internal, err)
	}

	return achievements, nil
}

// Event-driven achievement checking
func (s *service) CheckAchievements(ctx context.Context, userID string) error {
	// Check all available achievements
	// For now, we only have DNA achievement
	if err := s.CheckDNAAchievement(ctx, userID); err != nil {
		// Log error but don't fail the entire check process
		// This allows other achievements to be checked even if one fails
		log.Printf("Failed to check DNA achievement for user %s: %v", userID, err)
	}

	return nil
}

// DNA Achievement implementation
func (s *service) CheckDNAAchievement(ctx context.Context, userID string) error {
	const dnaAchievementID = "DNA"

	// Step 1: Check if user already has DNA achievement (fail-fast)
	hasAchievement, err := s.Repository.HasAchievement(ctx, userID, dnaAchievementID)
	if err != nil {
		return errors.New(errors.Service, "failed to check if user has DNA achievement", errors.Internal, err)
	}
	if hasAchievement {
		// User already has the achievement, no need to check further
		return nil
	}

	// Step 2: Check if user completed "perfil-financeiro" challenge (fail-fast)
	hasCompletedChallenge, err := s.hasCompletedPerfilFinanceiroChallenge(ctx, userID)
	if err != nil {
		return errors.New(errors.Service, "failed to check perfil-financeiro challenge completion", errors.Internal, err)
	}
	if !hasCompletedChallenge {
		// Challenge not completed, user doesn't qualify for DNA achievement
		return nil
	}

	// Step 3: Check if all family members have financial status (fail-fast)
	hasCompletedDNA, err := s.hasCompletedFinancialDNA(ctx, userID)
	if err != nil {
		return errors.New(errors.Service, "failed to check financial DNA completion", errors.Internal, err)
	}
	if !hasCompletedDNA {
		// DNA not completed, user doesn't qualify for DNA achievement
		return nil
	}

	// Step 4: All checks passed, award the achievement
	return s.awardAchievement(ctx, userID, dnaAchievementID)
}

// Helper methods for DNA achievement checks

func (s *service) hasCompletedPerfilFinanceiroChallenge(ctx context.Context, userID string) (bool, error) {
	progression, err := s.ProgressionRepo.FindByUser(ctx, userID)
	if err != nil {
		return false, err
	}

	// Look for "perfil-financeiro" phase completion in any trail
	for _, trail := range progression.Trails {
		if trail.Challenge != nil {
			for _, phase := range trail.Challenge.Phases {
				if phase.Identifier == "perfil-financeiro" && phase.Completed {
					return true, nil
				}
			}
		}
	}

	return false, nil
}

func (s *service) hasCompletedFinancialDNA(ctx context.Context, userID string) (bool, error) {
	financialDNA, err := s.FinancialDNARepo.FindByUser(ctx, userID)
	if err != nil {
		return false, err
	}

	// Check if all family members have financial status populated
	// Exclude children and grandchildren from the check as per requirements
	for _, member := range financialDNA.Members {
		// Skip children and grandchildren (they have parent IDs)
		if member.FinancialStatus == nil || !member.FinancialStatus.IsPopulated {
			// Check if this is a parent/grandparent (no parent IDs) or if it's a required member
			// For now, we'll check all members except those explicitly marked as children
			if !s.isMemberChild(member, financialDNA.Members) {
				return false, nil
			}
		}
	}

	return true, nil
}

func (s *service) isMemberChild(member *FamilyMemberData, allMembers []*FamilyMemberData) bool {
	// A member is considered a child if they have parent relationships
	// This is a simplified check - in a real implementation you might want more sophisticated logic
	return len(member.Name) > 0 && (member.Name == "child" || member.Name == "grandchild")
}

func (s *service) awardAchievement(ctx context.Context, userID, achievementID string) error {
	// Create the user achievement record
	userAchievement := &gamification.UserAchievement{
		UserID:        userID,
		AchievementID: achievementID,
	}

	err := s.Repository.Create(ctx, userAchievement)
	if err != nil {
		return errors.New(errors.Service, "failed to award achievement", errors.Internal, err)
	}

	log.Printf("Awarded %s achievement to user %s", achievementID, userID)
	return nil
}
