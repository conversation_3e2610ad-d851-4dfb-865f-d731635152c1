package gamification

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// UserAchievement represents a user's earned achievement in the gamification system
type UserAchievement struct {
	ObjectID      primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	ID            string             `json:"id,omitempty" bson:"-"`
	UserID        string             `json:"userId" bson:"userId"`
	AchievementID string             `json:"achievementId" bson:"achievementId"`
	EarnedAt      time.Time          `json:"earnedAt" bson:"earnedAt"`
	CreatedAt     time.Time          `json:"createdAt" bson:"createdAt"`
	UpdatedAt     time.Time          `json:"updatedAt" bson:"updatedAt"`
}

// PrepareCreate prepares the user achievement for creation
func (ua *UserAchievement) PrepareCreate() {
	now := time.Now()
	ua.EarnedAt = now
	ua.CreatedAt = now
	ua.UpdatedAt = now
}

// PrepareUpdate prepares the user achievement for update
func (ua *UserAchievement) PrepareUpdate() {
	ua.UpdatedAt = time.Now()
}

// SetID sets the ID from ObjectID
func (ua *UserAchievement) SetID() {
	if !ua.ObjectID.IsZero() {
		ua.ID = ua.ObjectID.Hex()
	}
}
